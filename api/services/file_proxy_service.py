import json
import logging
from typing import Generator

from client.ois_s3_client import OisS3Client, ClientOptions
from client.ois_service_client import OISServiceOptions
from werkzeug.exceptions import NotFound

from configs import dify_config

logger = logging.getLogger(__name__)
logging.getLogger('root').setLevel(dify_config.LOG_LEVEL)

class FileProxyService:

    ois_client = OisS3Client(
        client_options=ClientOptions(
            env=dify_config.FILE_PROXY_OIS_ENV,
            region=dify_config.FILE_PROXY_OIS_REGION,
            app_id=dify_config.FILE_PROXY_OIS_APP_ID,
            ois_service_url=dify_config.FILE_PROXY_OIS_SERVICE_URL,
            idaas_client_id=dify_config.FILE_PROXY_OIS_IDAAS_CLIENT_ID,
            idaas_client_secret=dify_config.FILE_PROXY_OIS_IDAAS_CLIENT_SECRET,
            idaas_service_id=dify_config.FILE_PROXY_OIS_IDAAS_SERVICE_ID,
            idaas_url=dify_config.FILE_PROXY_OIS_IDAAS_URL
        ),
        optional_config=OISServiceOptions(
            logger=logger
        )
    )
    logging.basicConfig(level=dify_config.LOG_LEVEL) # 清除 OiS Client 的默认日志配置
    bucket_name = dify_config.FILE_PROXY_OIS_BUCKET

    @classmethod
    def get_image_preview(cls, document_id: str, file_key: str) -> Generator:
        """
        获取图片预览的生成器和MIME类型
        """
        try:

            response = cls.ois_client.get_object(bucket=cls.bucket_name, object_key=file_key)
            if not response or not response.is_succeed():
                raise NotFound(f"File not found or error occurred: {response.message}")

            yield from response.data.iter_chunks()
        except Exception as e:
            logger.warning(f"File proxy service error: {e}", e)
