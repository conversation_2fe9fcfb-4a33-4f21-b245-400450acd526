from flask import Blueprint

from libs.external_api import ExternalApi

bp = Blueprint("inner_api", __name__, url_prefix="/inner/api")
api = ExternalApi(bp)

from .subscription import model_subscription,mcp_subscription
from .account import account
from .workspace import account, workspace, tool_providers, models, app
from .cloudevent import dispacher
from .datasets import external, datasets, datasets_document
from . import apikey
