from cloudevents.http import from_http
from flask import request
from flask_restful import Resource

from controllers.console.app.app import ALLOW_CREATE_APP_MODES
from controllers.console.wraps import setup_required
from controllers.inner_api import api
from controllers.inner_api.wraps import inner_api_only
from extensions.ext_database import db
from models.model import App
from services.app_service import AppService


class EnterpriseAppEvent(Resource):
    @setup_required
    @inner_api_only
    def post(self):
        event = from_http(request.headers, request.get_data())

        if event.get_attributes()['type'] == 'cloud.chj.li.oam.component.deleted':
            component = event.get_data()['data']
            if component and component['componentType'] in ALLOW_CREATE_APP_MODES and component['extensions']['difyApp']['appId']:
                app = db.session.query(App).filter_by(id=component['extensions']['difyApp']['appId']).first()
                if app:
                    app_service = AppService()
                    app_service.delete_app(app)
                return {
                    'message': 'app deleted'
                }, 200

        return "", 200


api.add_resource(EnterpriseAppEvent, '/enterprise/workspace/app/remove')
