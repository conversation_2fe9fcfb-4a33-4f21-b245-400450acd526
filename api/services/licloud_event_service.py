import logging
import uuid
from datetime import datetime, timezone

from cloudevents.http import Cloud<PERSON>vent

from extensions.ext_database import db
from models.account import A<PERSON>unt<PERSON><PERSON><PERSON>, Tenant, TenantAccountJoin
from services.account_service import AccountService, RegisterService, TenantService
from services.licloud_iam_service import LiCloudIamService


class LiCloudEventService:

    def tenant_member_join(self, event: CloudEvent):
        """
          租户成员加入事件处理
        :param event: CloudEvent
        :return: None

        event example:
        {
            "specversion": "1.0",
            "id": "6fc07fdd-eb31-48ec-8f5c-15f79d79d1e1",
            "source": "https://li.chj.cloud/events/licloud-iam-service",
            "type": "cloud.chj.li.iam.tenant.members.join",
            "subject": "",
            "time": "2023-12-14T08:23:08.31977+08:00",
            "tenantid": "jmttvo",
            "datacontenttype": "application/json",
            "dataschema": "",
            "data": {
                "createAt": "2023-12-14 08:23:08",
                "expiredAt": "2023-12-14 08:23:08",
                "operator": "zhangsan",
                "username": "lisi"
            }
        }
        """
        username = event.get_data()['username']
        if username is None:
            raise ValueError('username is required.')

        account = AccountService.get_account_by_name(username)

        if account is not None:
            logging.info(f"LiCloudCloudEventService: account {username} already exists.")
            return

        user = LiCloudIamService().get_user_info_by_username(username=username)
        email = user['email']

        try:

            db.session.begin_nested()

            open_id = username
            provider = 'idaas'
            language = 'zh-Hans'

            account = AccountService.create_account(
                email=email,
                name=username,
                interface_language=language,
                password=None
            )
            account.status = AccountStatus.ACTIVE.value
            account.initialized_at = datetime.now(timezone.utc).replace(tzinfo=None)

            if open_id is not None or provider is not None:
                AccountService.link_account_integrate(provider, open_id, account)

            db.session.commit()
        except Exception as e:
            db.session.rollback()
            logging.error(f"AccountInitAPI: Failed to register user {username}: {str(e)}")

        logging.info("LiCloudCloudEventService: account created.")
        return

    @staticmethod
    def tenant_member_leave(event: CloudEvent):
        """
        租户成员离开事件处理

        event example:
        {
            "username": "zhangsan",
            "operator": "lisi"
        }
        """

        tid = event.get('tenantid')
        username = event.get_data()['username']
        account = AccountService.get_account_by_name(username)
        if account is None:
            logging.warning(f"LiCloudCloudEventService: account {username} not found.")
            return

        db.session.begin_nested()
        joined_tenants = TenantService.get_join_tenants(account)
        for tenant in joined_tenants:
            if not tenant.custom_config or tenant.custom_config_dict['licloud_tenant_id'] != tid:
                continue

            TenantAccountJoin.query.filter_by(tenant_id=tenant.id, account_id=account.id).delete()

        db.session.commit()

    @staticmethod
    def tenant_group_member_leave(event: CloudEvent):
        """
        租户组成员离开事件处理

        event example:
        {
            "createdBy": "litong",
            "groupName": "TENANT_ADMINISTRATOR",
            "updateAt": "2023-12-14 08:22:38",
            "updateBy": "litong",
            "username": "lisi"
        }
        """

        tid = event.get('tenantid')
        username = event.get_data()['username']
        account = AccountService.get_account_by_name(username)
        if account is None:
            logging.warning(f"LiCloudCloudEventService: account {username} not found.")
            return

        # 离开租户管理员组、SRE组时，清除自动添加到 dify workspace 的成员
        if event.get_data()['groupName'] in ['TENANT_ADMINISTRATOR', 'TENANT_SRE_GROUP']:
            db.session.begin_nested()
            joined_tenants = TenantService.get_join_tenants(account)
            for tenant in joined_tenants:
                if not tenant.custom_config or tenant.custom_config_dict['licloud_tenant_id'] != tid:
                    continue

                (TenantAccountJoin.query
                    .filter_by(tenant_id=tenant.id, account_id=account.id, invited_by=uuid.UUID(int=0))
                    .delete()
                )
            db.session.commit()

