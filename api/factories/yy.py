from core.file import File
from core.variables import FileVariable
from factories.variable_factory import build_environment_variable_from_mapping

if __name__ == '__main__':
    # mapping = {
    #     'description': '',
    #     'id': '2d94bc67-1783-423d-aa73-7d7ff0caca29',
    #     'name': 'example_image_01',
    #     'selector': ['env', 'example_image_01'],
    #     'value': {
    #         'tenant_id': '2d94bc67-1783-423d-aa73-7d7ff0caca29',
    #         'extension': '.png',
    #         'filename': '20240723093353675367.png',
    #         'mime_type': 'image/png',
    #         'remote_url': 'https://jg.s-ns.com/i001/upfiles/image/202407/20240723093353675367.png',
    #         'size': 466468,
    #         'transfer_method': 'local_file',
    #         'type': 'image',
    #         'storage_key': '',
    #         'related_id': '20240723093353675367'
    #     },
    #     'value_type': 'file'
    # }
    # rest = build_environment_variable_from_mapping(mapping)
    # print(rest)
    file_dict = {
        "dify_model_identity": "__dify__file__",
        "id": None,
        "tenant_id": "fe2d767b-2ff9-4452-a14b-f8a24de5725d",
        "type": "image",
        "transfer_method": "local_file",
        "remote_url": "",
        "related_id": "8b78fce4-a166-48d0-81e8-984f73c2f17c",
        "filename": "ccc_example.jpeg",
        "extension": ".jpeg",
        "mime_type": "image/jpeg",
        "size": 128474
    }
    file = File.from_dict(file_dict)
    fvb=FileVariable(id="id", name="name", value=file, selector=["env"])
    print(file)
    print(fvb)
