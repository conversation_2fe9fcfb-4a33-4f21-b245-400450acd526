from flask_login import current_user
from flask_restful import Resource, reqparse, marshal_with

from controllers.console import api
from controllers.console.app.error import BindFeishuAppNotFoundError
from controllers.console.app.wraps import get_app_model
from controllers.console.wraps import setup_required
from controllers.console.wraps import account_initialization_required
from fields.feishu_app_fields import feishu_app_fields
from libs.login import login_required


class BindFeishuAppApi(Resource):

    @setup_required
    @login_required
    @account_initialization_required
    @get_app_model
    @marshal_with(feishu_app_fields)
    def post(self, app_model):
        """
        bind feishu app
        """
        from services.feishu.feishu_service import feishu_service

        parser = reqparse.RequestParser()

        parser.add_argument("feishu_app_name", type=str, required=True, location="json")
        parser.add_argument("feishu_app_id", type=str, required=True, location="json")
        parser.add_argument("feishu_app_secret", type=str, required=True, location="json")
        parser.add_argument("encrypt_key", type=str, location="json")
        parser.add_argument("verification_token", type=str, location="json")
        parser.add_argument("call_app_inputs_json_path", type=dict, location="json")
        parser.add_argument("enable_prologue", type=bool, location="json", required=False, default=False)
        parser.add_argument("enable_reference", type=bool, location="json", required=False, default=False)
        args = parser.parse_args()

        return feishu_service.bind_feishu_app(app_model, args, current_user)

    @login_required
    @setup_required
    @account_initialization_required
    @get_app_model
    @marshal_with(feishu_app_fields)
    def get(self, app_model) -> dict:
        from services.feishu.feishu_service import feishu_service

        """
        get bind feishu app
        """
        # 从 path 中获取 app_id
        result = feishu_service.get_bind_feishu_app(app_model)
        if result is None:
            raise BindFeishuAppNotFoundError()
        return result


class FeishuWebhookApi(Resource):

    @setup_required
    @login_required
    def get(self, app_id):
        """
        feishu webhook
        """
        from services.feishu.feishu_service import feishu_service

        baseUrl = feishu_service.app.config.get("FEISHU_WEBHOOK_URL")
        return {
            "data": f"{baseUrl}/v1/apps/{app_id}/feishu-webhook"
        }


api.add_resource(BindFeishuAppApi, "/apps/<uuid:app_id>/feishu-app")
api.add_resource(FeishuWebhookApi, "/apps/<string:app_id>/generator-feishu-webhook")
