import json
from argparse import ArgumentTypeError
from flask import request
from flask_restful import Resource, fields, marshal, marshal_with, reqparse
from sqlalchemy import asc, desc
from transformers.hf_argparser import string_to_bool
from werkzeug.exceptions import Forbidden, NotFound

import services
from controllers.console.app.error import (
    ProviderModelCurrentlyNotSupportError,
    ProviderNotInitializeError,
    ProviderQuotaExceededError,
)
from controllers.console.datasets.error import DocumentIndexingError

from controllers.console.wraps import setup_required
from controllers.inner_api import api
from controllers.inner_api.wraps import inner_api_only
from core.errors.error import (
    ModelCurrentlyNotSupportError,
    ProviderTokenNotInitError,
    QuotaExceededError,
)
from extensions.ext_database import db
from fields.document_fields import (
    document_fields,
    document_with_segments_fields,
)
from libs.helper import uuid_value
from models import Dataset, Document, DocumentSegment, Tenant
from services.account_service import AccountService
from services.dataset_service import DatasetService, DocumentService
from services.entities.knowledge_entities.knowledge_entities import KnowledgeConfig


class EnterpriseDatasetDocumentListApi(Resource):
    @setup_required
    @inner_api_only
    def get(self, dataset_id):
        dataset_id = str(dataset_id)
        user_id = request.args.get("user_id", default=None, type=str)
        tenant_id = request.args.get("tenant_id", default=None, type=str)
        page = request.args.get("page", default=1, type=int)
        limit = request.args.get("limit", default=20, type=int)
        search = request.args.get("keyword", default=None, type=str)
        sort = request.args.get("sort", default="-created_at", type=str)

        current_user = AccountService.get_account_by_name(user_id)
        current_user.current_tenant_id = tenant_id

        # "yes", "true", "t", "y", "1" convert to True, while others convert to False.
        try:
            fetch = string_to_bool(request.args.get("fetch", default="false"))
        except (ArgumentTypeError, ValueError, Exception) as e:
            fetch = False
        dataset = DatasetService.get_dataset(dataset_id)
        if not dataset:
            raise NotFound("Dataset not found.")

        try:
            DatasetService.check_dataset_permission(dataset, current_user)
        except services.errors.account.NoPermissionError as e:
            raise Forbidden(str(e))

        query = Document.query.filter_by(dataset_id=str(dataset_id), tenant_id=current_user.current_tenant_id)

        if search:
            search = f"%{search}%"
            query = query.filter(Document.name.like(search))

        if sort.startswith("-"):
            sort_logic = desc
            sort = sort[1:]
        else:
            sort_logic = asc

        if sort == "hit_count":
            sub_query = (
                db.select(DocumentSegment.document_id, db.func.sum(DocumentSegment.hit_count).label("total_hit_count"))
                    .group_by(DocumentSegment.document_id)
                    .subquery()
            )

            query = query.outerjoin(sub_query, sub_query.c.document_id == Document.id).order_by(
                sort_logic(db.func.coalesce(sub_query.c.total_hit_count, 0)),
                sort_logic(Document.position),
            )
        elif sort == "created_at":
            query = query.order_by(
                sort_logic(Document.created_at),
                sort_logic(Document.position),
            )
        else:
            query = query.order_by(
                desc(Document.created_at),
                desc(Document.position),
            )

        paginated_documents = query.paginate(page=page, per_page=limit, max_per_page=100, error_out=False)
        documents = paginated_documents.items
        if fetch:
            for document in documents:
                completed_segments = DocumentSegment.query.filter(
                    DocumentSegment.completed_at.isnot(None),
                    DocumentSegment.document_id == str(document.id),
                    DocumentSegment.status != "re_segment",
                ).count()
                total_segments = DocumentSegment.query.filter(
                    DocumentSegment.document_id == str(document.id), DocumentSegment.status != "re_segment"
                ).count()
                document.completed_segments = completed_segments
                document.total_segments = total_segments
            data = marshal(documents, document_with_segments_fields)

        else:
            data = marshal(documents, document_fields)

        # Add feishu_permission to each document in the response data
        for doc_data, document in zip(data, documents):
            data_source_type = document.data_source_type
            if data_source_type == "feishu_import":
                feishu_url = document.data_source_info_dict.get('url')
                permission_result_view = DocumentService.check_user_feishu_document_permission(feishu_url, 'view')
                permission_result_edit = DocumentService.check_user_feishu_document_permission(feishu_url, 'edit')
                doc_data['has_feishu_view_permission'] = permission_result_view['result']
                doc_data['has_feishu_edit_permission'] = permission_result_edit['result']

        response = {
            "data": data,
            "has_more": len(documents) == limit,
            "limit": limit,
            "total": paginated_documents.total,
            "page": page,
        }

        return response

    documents_and_batch_fields = {"documents": fields.List(fields.Nested(document_fields)), "batch": fields.String}

    @setup_required
    @inner_api_only
    @marshal_with(documents_and_batch_fields)
    def post(self, dataset_id):
        dataset_id = str(dataset_id)

        dataset = DatasetService.get_dataset(dataset_id)

        if not dataset:
            raise NotFound("Dataset not found.")

        parser = reqparse.RequestParser()

        parser.add_argument('tenant_id', type=uuid_value, required=True, location='json')
        parser.add_argument('user_id', type=str, required=True, nullable=False, location='json')

        parser.add_argument(
            "indexing_technique", type=str, choices=Dataset.INDEXING_TECHNIQUE_LIST, nullable=False, location="json"
        )
        parser.add_argument("data_source", type=dict, required=False, location="json")
        parser.add_argument("process_rule", type=dict, required=False, location="json")
        parser.add_argument("duplicate", type=bool, default=True, nullable=False, location="json")
        parser.add_argument("original_document_id", type=str, required=False, location="json")
        parser.add_argument("doc_form", type=str, default="text_model", required=False, nullable=False, location="json")
        parser.add_argument(
            "doc_language", type=str, default="English", required=False, nullable=False, location="json"
        )
        parser.add_argument("retrieval_model", type=dict, required=False, nullable=False, location="json")
        args = parser.parse_args()

        if not dataset.indexing_technique and not args["indexing_technique"]:
            raise ValueError("indexing_technique is required.")

        user_id = args['user_id']
        tenant_id = args['tenant_id']
        current_user = AccountService.get_account_by_name(user_id)
        current_user._current_tenant = Tenant(id=tenant_id)

        # The role of the current user in the ta table must be admin, owner, or editor
        # if not current_user.is_dataset_editor:
        #     raise Forbidden()

        # try:
        #     DatasetService.check_dataset_permission(dataset, current_user)
        # except services.errors.account.NoPermissionError as e:
        #     raise Forbidden(str(e))
        #
        knowledge_config = KnowledgeConfig(**args)
        # validate args
        DocumentService.document_create_args_validate(knowledge_config)

        try:
            documents, batch = DocumentService.save_document_with_dataset_id(dataset, knowledge_config, current_user)
        except ProviderTokenNotInitError as ex:
            raise ProviderNotInitializeError(ex.description)
        except QuotaExceededError:
            raise ProviderQuotaExceededError()
        except ModelCurrentlyNotSupportError:
            raise ProviderModelCurrentlyNotSupportError()

        return {"documents": documents, "batch": batch}


class EnterpriseDocumentRenameApi(Resource):
    @setup_required
    @inner_api_only
    @marshal_with(document_fields)
    def post(self, dataset_id, document_id):
        dataset = DatasetService.get_dataset(dataset_id)
        parser = reqparse.RequestParser()
        parser.add_argument("name", type=str, required=True, nullable=False, location="json")
        args = parser.parse_args()
        name = args["name"]
        try:
            dataset = DatasetService.get_dataset(dataset_id)
            if not dataset:
                raise ValueError("Dataset not found.")

            document = DocumentService.get_document(dataset_id, document_id)

            if not document:
                raise ValueError("Document not found.")

            document.name = name

            db.session.add(document)
            db.session.commit()

            return document
        except services.errors.document.DocumentIndexingError:
            raise DocumentIndexingError("Cannot delete document during indexing.")


class EnterpriseDocumentUpdateSyscApi(Resource):
    @setup_required
    @inner_api_only
    @marshal_with(document_fields)
    def post(self, dataset_id, document_id):
        dataset = DatasetService.get_dataset(dataset_id)
        parser = reqparse.RequestParser()
        parser.add_argument("new_auto_sync", type=str, required=True, nullable=False, location="json")
        args = parser.parse_args()
        str_to_bool_mapping = {
            "true": True,
            "false": False
        }
        new_auto_sync = str_to_bool_mapping.get(args["new_auto_sync"].lower())
        try:
            dataset = DatasetService.get_dataset(dataset_id)
            if not dataset:
                raise ValueError("Dataset not found.")

            document_dataset = DocumentService.get_document(dataset_id, document_id)

            if document_dataset and hasattr(document_dataset, 'data_source_info'):
                try:
                    # 尝试将 data_source_info 转换为字典
                    if isinstance(document_dataset.data_source_info, str):
                        document_dataset.data_source_info = json.loads(document_dataset.data_source_info)
                    # 确保 data_source_info 是字典类型
                    if not isinstance(document_dataset.data_source_info, dict):
                        document_dataset.data_source_info = {}
                    document_dataset.data_source_info["auto_sync_enabled"] = new_auto_sync
                except json.JSONDecodeError:
                    # 若无法解析为 JSON，初始化为包含 auto_sync_enabled 的字典
                    document_dataset.data_source_info = {"auto_sync_enabled": new_auto_sync}
                    # 将 data_source_info 转换回字符串
                document_dataset.data_source_info = json.dumps(document_dataset.data_source_info)
                db.session.add(document_dataset)
                db.session.commit()

            return document_dataset
        except services.errors.document.DocumentIndexingError:
            raise DocumentIndexingError("Cannot delete document during indexing.")

class EnterpriseDocumentDeleteApi(Resource):
    @setup_required
    @inner_api_only
    def delete(self, dataset_id, document_id):
        dataset_id = str(dataset_id)
        document_id = str(document_id)
        try:
            dataset = DatasetService.get_dataset(dataset_id)
            if not dataset:
                raise ValueError("Dataset not found.")

            document = DocumentService.get_document(dataset_id, document_id)

            if not document:
                raise ValueError("Document not found.")

            DocumentService.delete_document(document)
        except services.errors.document.DocumentIndexingError:
            raise DocumentIndexingError("Cannot delete document during indexing.")

        return {"result": "success"}, 204


class EnterpriseDocumentUpdateApi(Resource):
    @setup_required
    @inner_api_only
    def post(self, dataset_id, document_id):
        try:

            from schedule.sync_feishu_documents_task import document_update_for_document_id

            status = document_update_for_document_id(dataset_id=dataset_id, document_id=document_id)
            if status:
                return {"result": f"{status}"}, 404

            else:
                return {"result": "success"}, 204

        except Exception as e:

            return {"result": f"{e}"}, 404


api.add_resource(EnterpriseDatasetDocumentListApi, "/enterprise/datasets/<uuid:dataset_id>/documents")
api.add_resource(EnterpriseDocumentRenameApi,
                 "/enterprise/datasets/<uuid:dataset_id>/documents/<uuid:document_id>/rename")
api.add_resource(EnterpriseDocumentUpdateSyscApi,
                 "/enterprise/datasets/<uuid:dataset_id>/documents/<uuid:document_id>/update_sync")
api.add_resource(EnterpriseDocumentDeleteApi, "/enterprise/datasets/<uuid:dataset_id>/documents/<uuid:document_id>")
api.add_resource(EnterpriseDocumentUpdateApi,
                 "/enterprise/datasets/<uuid:dataset_id>/documents/<uuid:document_id>/update")
