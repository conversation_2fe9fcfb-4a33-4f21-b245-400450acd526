import os

from configs import dify_config
from libs.httpx import httpx_client


class EnterpriseRequest:
    base_url = dify_config.ENTERPRISE_API_URL
    secret_key = dify_config.ENTERPRISE_API_SECRET_KEY

    proxies = {
        "http": "",
        "https": "",
    }

    @classmethod
    def send_request(cls, method, endpoint, json=None, params=None):
        headers = {"Content-Type": "application/json", "Enterprise-Api-Secret-Key": cls.secret_key}

        url = f"{cls.base_url}{endpoint}"
        response = httpx_client.request(method, url, json=json, params=params, headers=headers)

        return response.json()
