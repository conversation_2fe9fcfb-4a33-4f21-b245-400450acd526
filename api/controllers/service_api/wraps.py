import json
import logging
from collections.abc import Callable
from datetime import UTC, datetime, timedelta
from enum import Enum
from functools import wraps
from typing import Optional

from flask import current_app, request
from flask_login import user_logged_in  # type: ignore
from flask_restful import Resource  # type: ignore
from pydantic import BaseModel
from sqlalchemy import select, update
from sqlalchemy.orm import Session
from werkzeug.exceptions import Forbidden, Unauthorized, BadRequest

from configs import dify_config
from core.utils.feishu_auth_service import FeishuAuthorizationService
from core.utils.idaas.idaas_service import IDaaSM2MService
from extensions.ext_database import db
from libs.login import _get_user
from models.account import Account, Tenant, TenantAccountJoin, TenantStatus
from models.model import ApiToken, App, EndUser
from services.feature_service import FeatureService
from services.feishu.it_coa_service import ITCoaService
from .base62 import decode_uuid

import re
import uuid

class WhereisUserArg(Enum):
    """
    Enum for whereis_user_arg.
    """

    QUERY = "query"
    JSON = "json"
    FORM = "form"


class FetchUserArg(BaseModel):
    fetch_from: WhereisUserArg
    required: bool = False


def validate_app_token(view: Optional[Callable] = None, *, fetch_user_arg: Optional[FetchUserArg] = None):
    def decorator(view_func):
        @wraps(view_func)
        def decorated_view(*args, **kwargs):
            if request.headers.get("X-Inner-Api-Key"):
                app_id = validate_inner_api_token()
                app_model = db.session.query(App).filter(App.id == app_id).first()
            else:
                api_token = validate_and_get_api_token("app")
                app_model = db.session.query(App).filter(App.id == api_token.app_id).first()

            if not app_model:
                raise Forbidden("The app no longer exists.")

            if app_model.status != "normal":
                raise Forbidden("The app's status is abnormal.")

            if not app_model.enable_api:
                raise Forbidden("The app's API service has been disabled.")

            tenant = db.session.query(Tenant).filter(Tenant.id == app_model.tenant_id).first()
            if tenant is None:
                raise ValueError("Tenant does not exist.")
            if tenant.status == TenantStatus.ARCHIVE:
                raise Forbidden("The workspace's status is archived.")

            kwargs["app_model"] = app_model

            if fetch_user_arg:
                account_id,user_id,open_id = None, None, None

                # 根据配置从不同的请求数据源获取用户标识符，URL参数、JSON体、表单数据
                # user_id: 域账号，e.g.: zhangsan
                # open_id: IDaaS open_id
                if fetch_user_arg.fetch_from == WhereisUserArg.QUERY:
                    user_id = request.args.get("user")
                    open_id = request.args.get("open_id")
                    account_id = request.args.get("account_id")

                elif fetch_user_arg.fetch_from == WhereisUserArg.JSON:
                    user_id = request.get_json().get("user")
                    open_id = request.get_json().get("open_id")
                    account_id = request.get_json().get("account_id")

                elif fetch_user_arg.fetch_from == WhereisUserArg.FORM:
                    user_id = request.form.get("user")
                    open_id = request.form.get("open_id")
                    account_id = request.form.get("account_id")

                if account_id:
                    email = get_userid_by_email(email=account_id)

                    if email:
                        user_id = email

                    elif is_valid_uuid(account_id):
                        user_info = db.session.query(Account).filter(Account.id == account_id).first()
                        user_id = user_info.name

                    else:
                        user_id = account_id

                # 检查必要的用户标识是否提供
                if (not user_id and not open_id) and fetch_user_arg.required:
                    raise ValueError("Either user_id or open_id must be provided.")

                if user_id:
                    user_id = str(user_id)

                # 设置基础用户信息
                # 会话ID：默认域账号;如果没有域账号,则根据open_id从IDaaS获取域账号或昵称
                session_id = user_id or open_id
                # 外部用户ID：使用提供的任一ID
                external_user_id = user_id or open_id
                # 用户域账号
                user_name = user_id

                # 处理open_id情况：如果提供了open_id，则从IDaaS服务获取更详细的用户信息
                if open_id:
                    open_id = str(open_id)
                    try:
                        # 通过open_id从IDaaS获取账户信息
                        account = IDaaSM2MService().get_account_by_openid(open_id)

                        # 验证账户信息有效性
                        if not account:
                            logging.error("Invalid openId: %s", open_id)
                            raise Forbidden(f"Invalid openId: {open_id}")

                        # 更新用户名和会话ID
                        if account.get("ldap_name"):
                            user_name = account.get("ldap_name")

                        # 会话ID优先使用ldap_name域账号，其次使用nickname
                        session_id = account.get("ldap_name", account.get("nickname", ""))
                    except Exception as e:
                        raise Forbidden(f"Invalid openId, get email error: {open_id}")

                # 如果有用户名，验证飞书API的授权
                if user_name:
                    # 调用飞书授权服务验证用户对服务API的访问权限
                    auth_result = FeishuAuthorizationService().verify_authorization_for_service_api(
                        app_model, user_name, request
                    )

                    # 如果auth_result不为None，表示授权失败
                    if auth_result:
                        raise Unauthorized(json.dumps(auth_result))

                # 创建或更新最终用户信息
                logging.info(
                    "Create or update end user for app_id: %s, user_name: %s session_id: %s, external_user_id: %s",
                    app_model.id,
                    user_name,
                    session_id,
                    external_user_id,
                )
                kwargs["end_user"] = create_or_update_end_user_for_user_id(app_model, user_name, session_id,
                                                                           external_user_id)

            return view_func(*args, **kwargs)

        return decorated_view

    if view is None:
        return decorator
    else:
        return decorator(view)


def cloud_edition_billing_resource_check(resource: str, api_token_type: str):
    def interceptor(view):
        def decorated(*args, **kwargs):
            api_token = validate_and_get_api_token(api_token_type)
            features = FeatureService.get_features(api_token.tenant_id)

            if features.billing.enabled:
                members = features.members
                apps = features.apps
                vector_space = features.vector_space
                documents_upload_quota = features.documents_upload_quota

                if resource == "members" and 0 < members.limit <= members.size:
                    raise Forbidden("The number of members has reached the limit of your subscription.")
                elif resource == "apps" and 0 < apps.limit <= apps.size:
                    raise Forbidden("The number of apps has reached the limit of your subscription.")
                elif resource == "vector_space" and 0 < vector_space.limit <= vector_space.size:
                    raise Forbidden("The capacity of the vector space has reached the limit of your subscription.")
                elif resource == "documents" and 0 < documents_upload_quota.limit <= documents_upload_quota.size:
                    raise Forbidden("The number of documents has reached the limit of your subscription.")
                else:
                    return view(*args, **kwargs)

            return view(*args, **kwargs)

        return decorated

    return interceptor


def cloud_edition_billing_knowledge_limit_check(resource: str, api_token_type: str):
    def interceptor(view):
        @wraps(view)
        def decorated(*args, **kwargs):
            api_token = validate_and_get_api_token(api_token_type)
            features = FeatureService.get_features(api_token.tenant_id)
            if features.billing.enabled:
                if resource == "add_segment":
                    if features.billing.subscription.plan == "sandbox":
                        raise Forbidden(
                            "To unlock this feature and elevate your Dify experience, please upgrade to a paid plan."
                        )
                else:
                    return view(*args, **kwargs)

            return view(*args, **kwargs)

        return decorated

    return interceptor


def validate_dataset_token(view=None):
    def decorator(view):
        @wraps(view)
        def decorated(*args, **kwargs):
            api_token = validate_and_get_api_token("dataset")
            tenant_account_join = (
                db.session.query(Tenant, TenantAccountJoin)
                .filter(Tenant.id == api_token.tenant_id)
                .filter(TenantAccountJoin.tenant_id == Tenant.id)
                .filter(TenantAccountJoin.role.in_(["owner"]))
                .filter(Tenant.status == TenantStatus.NORMAL)
                .one_or_none()
            )  # TODO: only owner information is required, so only one is returned.
            if tenant_account_join:
                tenant, ta = tenant_account_join
                account = Account.query.filter_by(id=ta.account_id).first()
                # Login admin
                if account:
                    account.current_tenant = tenant
                    current_app.login_manager._update_request_context_with_user(account)  # type: ignore
                    user_logged_in.send(current_app._get_current_object(), user=_get_user())  # type: ignore
                else:
                    raise Unauthorized("Tenant owner account does not exist.")
            else:
                raise Unauthorized("Tenant does not exist.")
            return view(api_token.tenant_id, *args, **kwargs)

        return decorated

    if view:
        return decorator(view)

    # if view is None, it means that the decorator is used without parentheses
    # use the decorator as a function for method_decorators
    return decorator


def validate_and_get_api_token(scope: str | None = None):
    """
    Validate and get API token.
    """
    auth_header = request.headers.get("Authorization")
    if auth_header is None or " " not in auth_header:
        raise Unauthorized("Authorization header must be provided and start with 'Bearer'")

    auth_scheme, auth_token = auth_header.split(None, 1)
    auth_scheme = auth_scheme.lower()

    if auth_scheme != "bearer":
        raise Unauthorized("Authorization scheme must be 'Bearer'")

    app_id = None
    public_path = request.headers.get("x-agentops-public-path")
    if public_path:
        try:
            app_id_base62 = public_path.split("/")[1]
            app_id = decode_uuid(app_id_base62)
        except Exception as e:
            logging.warning("Failed to decode app_id_base62: %s", str(e))
            raise Unauthorized("Access token is invalid")

    current_time = datetime.now(UTC).replace(tzinfo=None)
    cutoff_time = current_time - timedelta(minutes=1)
    with Session(db.engine, expire_on_commit=False) as session:
        update_stmt = (
            update(ApiToken)
            .where(
                ApiToken.token == auth_token,
                (ApiToken.last_used_at.is_(None) | (ApiToken.last_used_at < cutoff_time)),
                ApiToken.type == scope,
            )
            .values(last_used_at=current_time)
            .returning(ApiToken)
        )
        result = session.execute(update_stmt)
        api_token = result.scalar_one_or_none()

        if not api_token:
            stmt = select(ApiToken).where(ApiToken.token == auth_token, ApiToken.type == scope)
            api_token = session.scalar(stmt)
            if not api_token:
                raise Unauthorized("Access token is invalid")
        else:
            session.commit()

    if app_id and app_id != api_token.app_id:
        raise Unauthorized("Access token is invalid, the token does not belong to the app")

    return api_token


def create_or_update_end_user_for_user_id(app_model: App,
                                          user_name: Optional[str] = None,
                                          session_id: Optional[str] = None,
                                          external_user_id: Optional[str] = None) -> EndUser:
    """
    Create or update session terminal based on user ID.
    """
    if not session_id:
        session_id = "DEFAULT-USER"

    end_user = (
        db.session.query(EndUser)
        .filter(
            EndUser.tenant_id == app_model.tenant_id,
            EndUser.app_id == app_model.id,
            EndUser.name == user_name,
            EndUser.session_id == session_id,
            EndUser.type == "service_api",
        )
        .first()
    )

    if end_user is None:
        end_user = EndUser(
            tenant_id=app_model.tenant_id,
            app_id=app_model.id,
            type="service_api",
            is_anonymous=session_id == "DEFAULT-USER",
            name=user_name,
            session_id=session_id,
            external_user_id=external_user_id,
        )
        db.session.add(end_user)
        db.session.commit()

    return end_user


class DatasetApiResource(Resource):
    method_decorators = [validate_dataset_token]


def validate_inner_api_token() -> str:
    """
    Validate and get API inner token.
    """
    inner_api_key = request.headers.get("X-Inner-Api-Key")
    if not inner_api_key or inner_api_key != dify_config.INNER_API_KEY:
        raise Unauthorized("Inner API key is invalid")

    app_id = request.headers.get("X-App-Id")
    if not app_id:
        raise BadRequest("App ID must be provided for header 'X-App-Id'")

    return app_id




def get_userid_by_email(email):
    pattern = r'^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\.[a-zA-Z0-9-.]+$'
    if re.fullmatch(pattern, email):
        account = Account.query.filter_by(email=email).first()
        if not account:
            username = ITCoaService().get_user_by_email(user_email=email)
            if not username:
                return None
            else:
                return username
        return account.name
    return None


def is_valid_uuid(uuid_str):
    try:
        uuid.UUID(uuid_str)

        return True

    except Exception as e:
        return False