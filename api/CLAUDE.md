# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Build and Development Commands

### Environment Setup
```bash
# Create and activate poetry environment
poetry env use 3.12
poetry install

# Install with specific dependency groups
poetry install --with dev,lint
```

### Database Operations
```bash
# Run database migrations
poetry run python -m flask db upgrade

# Create new migration
poetry run python -m flask db migrate -m "description"
```

### Development Server
```bash
# Start the main API server (development mode)
poetry run python -m flask run --host 0.0.0.0 --port=5001 --debug

# Start Celery worker for async tasks
poetry run python -m celery -A app.celery worker -P gevent -c 1 --loglevel INFO -Q dataset,generation,mail,ops_trace,app_deletion
```

### Testing
```bash
# Run all tests
poetry run -P api bash dev/pytest/pytest_all_tests.sh

# Run specific test
poetry run pytest tests/path/to/test_file.py
```

### Linting and Code Quality
```bash
# Run linting with ruff
poetry run ruff check .

# Run type checking with mypy
poetry run mypy .

# Check environment file syntax
poetry run dotenv-linter
```

## High-Level Architecture

### Core Components

**Dify Backend API** - A Flask-based AI application backend that provides:
- Multi-modal AI conversation and completion APIs
- Dataset management and knowledge base integration
- Workflow engine for complex AI automations
- Tool integration system including MCP (Model Context Protocol) support
- Multi-tenant workspace management

### Key Architectural Patterns

**MCP Integration Layer** (`core/mcp/`)
- Recently added Model Context Protocol support for external tool providers
- `MCPToolProvider` and `MCPTool` classes handle remote tool integration
- Session management for persistent MCP connections
- Authentication and authorization flows for MCP servers

**Tool Provider System** (`core/tools/`)
- Plugin architecture supporting multiple tool types: builtin, API-based, workflow, and MCP
- Dynamic tool discovery and parameter transformation
- Tool execution engine with callback handlers

**Workflow Engine** (`core/workflow/`)
- Graph-based execution engine for complex AI workflows
- Node-based architecture with various node types (LLM, tool, code execution, etc.)
- Variable management and template parsing system
- Parallel execution support with depth limits

**Model Runtime** (`core/model_runtime/`)
- Abstraction layer for various LLM providers (OpenAI, Anthropic, local models, etc.)
- Unified interface for text generation, embeddings, and reranking
- Provider credential management and validation

### Storage and Database

**Multi-tenant PostgreSQL Schema**
- Tenant-isolated data with workspace-based access control
- Alembic migrations for schema versioning
- SQLAlchemy ORM with model relationships

**Vector Database Integration**
- Support for 20+ vector databases (Weaviate, Qdrant, Milvus, etc.)
- Configurable through environment variables
- Used for knowledge base embeddings and retrieval

**File Storage Abstraction**
- Multiple storage backends: S3, Azure Blob, Google Cloud, Aliyun OSS, etc.
- OpenDAL integration for unified storage interface
- File upload limits and type validation

### Configuration Management

**Environment-based Configuration**
- Extensive `.env` configuration for all services
- Feature flags and deployment-specific settings
- Apollo configuration center integration for remote settings

**Poetry Dependency Management**
- Organized dependency groups: main, dev, lint, tools, storage, vdb
- Dependency pinning for reproducible builds
- Custom PyPI source for internal packages

### API Structure

**Multi-API Design**
- Console API: Administrative interface for workspace management
- Service API: Public API for AI applications
- Web API: Direct web application interface
- Inner API: Internal service communication

**Authentication & Authorization**
- JWT-based authentication with refresh tokens
- API key authentication for service APIs
- Role-based access control within workspaces

### Background Processing

**Celery Integration**
- Redis-backed message broker for async task processing
- Dedicated queues for different task types
- Gevent worker pool for I/O intensive tasks

## Important Development Notes

- The codebase uses Poetry for dependency management, not pip
- Database changes require Alembic migrations
- All new features should support multi-tenancy
- MCP tools are a recent addition for external tool integration
- Vector database choice affects indexing and retrieval performance
- Background tasks use Celery with specific queue routing
- Environment configuration is extensive - check `.env.example` for all options