import logging
from pathlib import Path

from core.agent.strategy.builtin_agent_strategy_manager import BuiltinAgentStrategyManager
from core.agent.strategy.builtin_agent_strategy_provider import BuiltinAgentStrategyProviderController

logger = logging.getLogger(__name__)

class AgentStrategyProviderService:

    @classmethod
    def get_provider_list(cls) -> list[BuiltinAgentStrategyProviderController]:
        """
        Get the list of agent strategy providers.

        :return: List of agent strategy providers.
        """
        return list(BuiltinAgentStrategyManager.list_agent_providers())

    @classmethod
    def get_provider_icon(cls, provider: str):
        """
        get agent provider icon and it's mimetype
        """
        icon_path, mime_type = BuiltinAgentStrategyManager.get_builtin_provider_icon(provider)
        icon_bytes = Path(icon_path).read_bytes()

        return icon_bytes, mime_type