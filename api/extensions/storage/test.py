from extensions.storage.s3_conjur_storage import ConjurClient

if __name__ == '__main__':
    conjur_client = ConjurClient(
        token_file_path="/Users/<USER>/Code/ai/licloud-dify/api/extensions/storage/access-token")
    username_path = "Prd_Vault/authn/App_licloud-dify_All/App_licloud-dify_BaiduCloud_AKSK/username"
    password_path = "Prd_Vault/authn/App_licloud-dify_All/App_licloud-dify_BaiduCloud_AKSK/password"
    username = conjur_client.get_secret(username_path)
    password = conjur_client.get_secret(password_path)

    if username:
        print(f"Username: {username}")

    if password:
        print(f"Password: {password}")
