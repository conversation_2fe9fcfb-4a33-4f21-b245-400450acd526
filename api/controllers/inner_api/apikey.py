import flask_restful
from flask_restful import Resource, fields, marshal_with
from controllers.inner_api import api
from extensions.ext_database import db
from libs.helper import TimestampField
from models.model import ApiToken, App

api_key_fields = {
    "id": fields.String,
    "type": fields.String,
    "token": fields.String,
    "last_used_at": TimestampField,
    "created_at": TimestampField,
}

api_key_list = {"data": fields.List(fields.Nested(api_key_fields), attribute="items")}


def _get_resource(resource_id, tenant_id, resource_model):
    resource = resource_model.query.filter_by(id=resource_id, tenant_id=tenant_id).first()

    if resource is None:
        flask_restful.abort(404, message=f"{resource_model.__name__} not found.")

    return resource


class EnterpriseBaseApiKeyListResource(Resource):
    resource_type = None
    resource_model = None
    resource_id_field = None
    token_prefix = None
    max_keys = 10

    @marshal_with(api_key_list)
    def get(self, resource_id):
        resource_id = str(resource_id)
        tenant_id = request.args.get("tenant_id", default=None, type=str)
        _get_resource(resource_id, tenant_id, self.resource_model)
        keys = (
            db.session.query(ApiToken)
            .filter(ApiToken.type == self.resource_type, getattr(ApiToken, self.resource_id_field) == resource_id)
            .all()
        )
        return {"items": keys}

    @marshal_with(api_key_fields)
    def post(self, resource_id):
        resource_id = str(resource_id)
        parser = reqparse.RequestParser()

        parser.add_argument('tenant_id', type=uuid_value, required=True, location='json')
        args = parser.parse_args()
        tenant_id = args.get('tenant_id')

        _get_resource(resource_id, tenant_id, self.resource_model)

        current_key_count = (
            db.session.query(ApiToken)
            .filter(ApiToken.type == self.resource_type, getattr(ApiToken, self.resource_id_field) == resource_id)
            .count()
        )

        if current_key_count >= self.max_keys:
            flask_restful.abort(
                400,
                message=f"Cannot create more than {self.max_keys} API keys for this resource type.",
                code="max_keys_exceeded",
            )

        key = ApiToken.generate_api_key(self.token_prefix, 24)
        api_token = ApiToken()
        setattr(api_token, self.resource_id_field, resource_id)
        api_token.tenant_id = tenant_id
        api_token.token = key
        api_token.type = self.resource_type
        db.session.add(api_token)
        db.session.commit()
        return api_token, 201


class EnterpriseBaseApiKeyResource(Resource):
    resource_type = None
    resource_model = None
    resource_id_field = None

    def delete(self, resource_id, api_key_id):
        pass


class EnterpriseAppApiKeyListResource(EnterpriseBaseApiKeyListResource):
    def after_request(self, resp):
        resp.headers["Access-Control-Allow-Origin"] = "*"
        resp.headers["Access-Control-Allow-Credentials"] = "true"
        return resp

    resource_type = "app"
    resource_model = App
    resource_id_field = "app_id"
    token_prefix = "app-"


class EnterpriseAppApiKeyResource(EnterpriseBaseApiKeyResource):
    def after_request(self, resp):
        resp.headers["Access-Control-Allow-Origin"] = "*"
        resp.headers["Access-Control-Allow-Credentials"] = "true"
        return resp

    resource_type = "app"
    resource_model = App
    resource_id_field = "app_id"


api.add_resource(EnterpriseAppApiKeyListResource, "/enterprise/apps/<uuid:resource_id>/api-keys")
api.add_resource(EnterpriseAppApiKeyResource, "/enterprise/apps/<uuid:resource_id>/api-keys/<uuid:api_key_id>")
