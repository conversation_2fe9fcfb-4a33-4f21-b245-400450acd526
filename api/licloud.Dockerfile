# base image
FROM artifactory.ep.chehejia.com/docker-remote/python:3.12-slim-bookworm AS base

WORKDIR /app/api

# Install Poetry
ENV POETRY_VERSION=2.1.3

# if you located in China, you can use aliyun mirror to speed up
# RUN pip install --no-cache-dir poetry==${POETRY_VERSION} -i https://mirrors.aliyun.com/pypi/simple/

RUN pip install --no-cache-dir --upgrade -i https://artifactory.ep.chehejia.com/artifactory/api/pypi/licloud-pypi/simple pip poetry==${POETRY_VERSION}

# Configure Poetry
ENV POETRY_CACHE_DIR=/tmp/poetry_cache
ENV POETRY_NO_INTERACTION=1
ENV POETRY_VIRTUALENVS_IN_PROJECT=true
ENV POETRY_VIRTUALENVS_CREATE=true
ENV POETRY_REQUESTS_TIMEOUT=15
ENV VIRTUAL_ENV=/app/api/.venv
ENV PATH="${VIRTUAL_ENV}/bin:${PATH}"


FROM base AS packages

RUN sed -i "s@http://deb.debian.org@https://artifactory.ep.chehejia.com/artifactory@g" /etc/apt/sources.list.d/debian.sources && \
    apt-get update && \
    apt-get install -y --no-install-recommends apt-transport-https ca-certificates && \
    apt-get install -y --no-install-recommends gcc g++ libc-dev libffi-dev libgmp-dev libmpfr-dev libmpc-dev

# Install Python dependencies
COPY api/pyproject.toml api/poetry.lock ./
RUN poetry install --no-cache --no-root

# otel 增强
ARG CACHEBUST=1.1.0.rc19
ENV PYTHON_AGENT_PATH="https://python-agent.oss-rg-china-mainland.aliyuncs.com/1.1.0.rc19/aliyun-python-agent.tar.gz"
RUN pip config set global.index-url https://mirrors.aliyun.com/pypi/simple/ && pip config set install.trusted-host mirrors.aliyun.com
RUN pip install aliyun-bootstrap==1.0.2 -i https://pypi.org/simple
RUN pip install lark-oapi
RUN aliyun-bootstrap -a install

# production stage
FROM base AS production

ENV FLASK_APP=app.py
ENV EDITION=SELF_HOSTED
ENV DEPLOY_ENV=PRODUCTION

EXPOSE 5001

# set timezone
ENV TZ=UTC

WORKDIR /app/api

RUN sed -i "s@http://deb.debian.org@https://artifactory.ep.chehejia.com/artifactory@g" /etc/apt/sources.list.d/debian.sources \
    && apt-get update \
    && apt-get install -y apt-transport-https ca-certificates  \
    && apt-get install -y --no-install-recommends curl wget vim procps net-tools nodejs ffmpeg libgmp-dev libmpfr-dev libmpc-dev \
    && apt-get install -y --no-install-recommends expat libldap-2.5-0 perl libsqlite3-0 zlib1g \
    && apt-get install -y --no-install-recommends fonts-noto-cjk libmagic1 \
    && apt-get autoremove -y \
    && rm -rf /var/lib/apt/lists/*

# Copy Python environment and packages
ENV VIRTUAL_ENV=/app/api/.venv
COPY --from=packages ${VIRTUAL_ENV} ${VIRTUAL_ENV}
ENV PATH="${VIRTUAL_ENV}/bin:${PATH}"

# Download nltk data
RUN python -c "import nltk; nltk.download('punkt'); nltk.download('averaged_perceptron_tagger')"

# Copy source code
COPY api /app/api/

# Copy entrypoint
COPY api/docker/entrypoint.sh /entrypoint.sh
RUN chmod +x /entrypoint.sh

ARG COMMIT_SHA
ENV COMMIT_SHA=${COMMIT_SHA}

ENTRYPOINT ["/bin/bash", "/entrypoint.sh"]