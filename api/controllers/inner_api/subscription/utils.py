from typing import Any, Optional, Dict
import httpx
import logging

from extensions.ext_database import db
from models.model import Tenant,Account
import requests

logger = logging.getLogger(__name__)

class ToolsClient(object):
    def __init__(self, timeout: int = 5, max_retries: int = 3):
        self.timeout = timeout
        self.max_retries = max_retries
        self.client = httpx.Client(timeout=self.timeout)

    def fetch_data(self, url, method, headers=None, params=None, data=None, max_retries=3)->dict[str, Any] or None:
        retries = 0
        while retries < max_retries:
            try:
                logger.debug(f"HttpxClient-fetch_data url: {url}, method: {method}, "
                             f"headers={headers}, params={params}, data={data}, timeout={self.timeout}")

                if method.upper() == 'GET':
                    response = self.client.get(url, headers=headers, params=params)

                elif method.upper() == 'POST':
                    response = self.client.post(url, headers=headers, json=data)

                else:
                    raise ValueError(f"Unsupported HTTP method: {method}")

                response.raise_for_status()

                logger.debug(f"HttpxClient-fetch_data response: {response}")

                return {"code": 200, "data": response.json().get("data", {})}

            except (httpx.NetworkError, httpx.TimeoutException) as e:
                retries += 1
                if retries >= max_retries:
                    logger.error(f"HttpxClient-fetch_data Max retries reached: {e}")
                    return {"code": 500, "info": f"Max retries reached: {e}"}

                wait_time = 2 ** retries
                logger.warning(
                    f"HttpxClient-fetch_data Retry {retries}/{max_retries} after {wait_time} seconds: {e}")

            except Exception as e:
                logger.error(f"HttpxClient-fetch_data Error: {e}")
                return {"code": 500, "info": f"{e}"}
        return None

    def get_llmgateway_info(self,routerkey:str) -> tuple[str,str] | None:
        llmgateway_rul = 'https://llm-gateway-dashboard.inner.chj.cloud/llm/api/model/'
        response = self.fetch_data(url=f"{llmgateway_rul}{routerkey}", method="GET")

        data = response.get("data")

        if response["code"] != 200 or not data:
            logger.warning(f"ToolsClient-get_llmgateway_info No data for routerkey: {routerkey}")
            return None

        modelProvider = data.get("modelProvider")
        model_type = data.get("model_type")

        return (modelProvider,model_type)

    def query_db_tenant_id(self,app_name:str)->str or None:
        try:
            tenant = db.session.query(Tenant).filter(Tenant.name == app_name).first()
            return tenant.id if tenant else None
        except Exception as e:
            logger.error(f"ToolsClient-query_db_tenant_id DB error: {e}")
            return None

    def query_db_account_id(self,username: str):
        try:
            account = db.session.query(Account).filter(Account.name == username).first()
            return account.id if account else None
        except Exception as e:
            logger.error(f"ToolsClient-query_db_account_id DB error: {e}")
            return None

    def add_tool_dify(self,**kwargs) -> dict | None:
        try:
            from services.tools.builtin_tools_manage_service import BuiltinToolManageService
            from sqlalchemy.orm import Session

            # 保存tool模型凭证
            with Session(db.engine) as session:
                BuiltinToolManageService.update_builtin_tool_provider(
                    session=session,
                    user_id=self.query_db_account_id(username=kwargs.get("username")),
                    tenant_id=kwargs.get("tenant_id"),
                    provider_name=kwargs.get("provider"),
                    credentials=kwargs.get("credentials"),
                )
                session.commit()

        except Exception as e:
            logger.error(f"ToolsClient-tool_dify_add DB error: {e}")
            return None


def get_mcp_details_by_code(code: str) -> Optional[Dict]:
    """
    根据 code 获取 MCP 详情信息

    Args:
        code: MCP 服务代码

    Returns:
        包含 MCP 详情信息的字典，包括 code、name、description、server_url、author
        如果未找到则返回 None
    """
    try:
        # 构建请求 URL
        api_url = f"https://llm-gateway-dashboard-dev.inner.chj.cloud/llm/api/mcp/{code}"

        # 设置请求头
        headers = {
            'Content-Type': 'application/json'
        }

        logger.info(f"正在请求 MCP 详情信息: {api_url}")

        # 发送 GET 请求
        response = requests.get(api_url, headers=headers, timeout=10)

        # 检查响应状态
        if response.status_code != 200:
            logger.error(f"请求 MCP 详情失败，状态码: {response.status_code}, 响应: {response.text}")
            return None

        # 解析响应数据
        response_data = response.json()

        # 检查响应格式
        if 'data' not in response_data:
            logger.error(f"响应数据格式错误，缺少 data 字段: {response_data}")
            return None

        data = response_data['data']

        # 提取需要的字段并转换为标准格式
        mcp_details = {
            'code': data.get('mcpAppCode', code),
            'name': data.get('mcpAppName', ''),
            'description': data.get('mcpAppDesc', ''),
            'server_url': data.get('mcpOnlineUrl', ''),
            'author': data.get('createdBy', '')
        }

        logger.info(f"成功获取 MCP 详情信息: {code} - {mcp_details['name']}")
        return mcp_details

    except requests.exceptions.Timeout:
        logger.error(f"请求 MCP 详情超时: {code}")
        return None
    except requests.exceptions.RequestException as e:
        logger.error(f"请求 MCP 详情网络错误: {code}, 错误: {e}")
        return None
    except ValueError as e:
        logger.error(f"解析 MCP 详情响应 JSON 失败: {code}, 错误: {e}")
        return None
    except Exception as e:
        logger.error(f"获取 MCP 详情信息失败: {code}, 错误: {e}")
        return None