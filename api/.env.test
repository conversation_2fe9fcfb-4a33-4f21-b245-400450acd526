# Your App secret key will be used for securely signing the session cookie
# Make sure you are changing this key for your deployment with a strong key.
# You can generate a strong key using `openssl rand -base64 42`.
# Alternatively you can set it with `SECRET_KEY` environment variable.
SECRET_KEY=3UMAIh6d78iCXk5SeJQv649rnsZlwvKfoL0vZ4VfD/kmiOApUfjD5NoU

# Console API base URL
# CONSOLE_API_URL=https://liai-builder-test.inner.chj.cloud
# CONSOLE_WEB_URL=https://liai-builder-test.inner.chj.cloud

# Service API base URL
# SERVICE_API_URL=https://liai-app-test.inner.chj.cloud

# Web APP base URL
# APP_WEB_URL=https://liai-app-test.inner.chj.cloud
# APP_API_URL=https://liai-app-test.inner.chj.cloud

# Files URL
FILES_URL = https://liai-app-test.inner.chj.cloud
# celery configuration
CELERY_BROKER_URL=redis://:<EMAIL>:16379/1

# redis configuration
REDIS_HOST=licloud-dify-test-default-test.chj.cloud
REDIS_PORT=16379
REDIS_USERNAME=
REDIS_PASSWORD=FacbaT+gwPgI
REDIS_DB=0

# PostgreSQL database configuration
DB_USERNAME=licloud_dify_rw
DB_PASSWORD='2QDTCEki6dRunIkr'
DB_HOST=licloud-dify-ontest.rdsgvax1sgwxout.rds.bj.baidubce.com
DB_PORT=3306
DB_DATABASE=licloud_dify_test

STORAGE_TYPE=s3

# S3 Storage configuration
S3_ENDPOINT=https://s3.bj.bcebos.com
S3_BUCKET_NAME=liai-ontest
S3_REGION=bj
#S3_ACCESS_KEY_CONJUR_PATH=Prd_Vault/authn/App_licloud-dify_All/App_licloud-dify_BaiduCloud_AKSK/username
#S3_SECRET_KEY_CONJUR_PATH=Prd_Vault/authn/App_licloud-dify_All/App_licloud-dify_BaiduCloud_AKSK/password
S3_ACCESS_KEY=ALTAKFYSq65RnkGFHcJBawZfP2
S3_SECRET_KEY=cbb3b7bb901345099c241c28318a88ee

# CORS configuration
WEB_API_CORS_ALLOW_ORIGINS=*
CONSOLE_CORS_ALLOW_ORIGINS=*

VECTOR_STORE=adbpg

# PGVector configuration
PGVECTOR_HOST=gp-2ze6zm21lq93c6kp1-master.gpdb.rds.aliyuncs.com
PGVECTOR_PORT=5432
PGVECTOR_USER=cloud_vector_test_rw
PGVECTOR_PASSWORD=44e22ieS45DXoot#kg466
PGVECTOR_DATABASE=cloud_vector_test
PGVECTOR_MIN_CACHED=10
PGVECTOR_MAX_CACHED=20
PGVECTOR_MAX_CONNECTIONS=100

# ADBPG 7.0 configuration
ADBPG_HOST=gp-2ze517f2y336mm9bb-master.gpdb.rds.aliyuncs.com
ADBPG_PORT=5432
ADBPG_USER=licloud_dify_vector_test_rw
ADBPG_PASSWORD=7eb41e7#2fe59
ADBPG_DATABASE=licloud_dify_vector_test
ADBPG_MIN_CACHED=10
ADBPG_MAX_CACHED=20
ADBPG_MAX_CONNECTIONS=100

# Upload configuration
UPLOAD_FILE_SIZE_LIMIT=15
UPLOAD_FILE_BATCH_LIMIT=5
UPLOAD_IMAGE_FILE_SIZE_LIMIT=10

# Model Configuration
MULTIMODAL_SEND_IMAGE_FORMAT=base64

# Sentry configuration
SENTRY_DSN=

# DEBUG
DEBUG=false
SQLALCHEMY_ECHO=false

# Notion import configuration,support public and internal
NOTION_INTEGRATION_TYPE=public
NOTION_CLIENT_SECRET=you-client-secret
NOTION_CLIENT_ID=you-client-id
NOTION_INTERNAL_SECRET=you-internal-secret

ETL_TYPE=dify
UNSTRUCTURED_API_URL=

SSRF_PROXY_HTTP_URL=http://*************:3128
SSRF_PROXY_HTTPS_URL=http://*************:3128

BATCH_UPLOAD_LIMIT=10
KEYWORD_DATA_SOURCE_TYPE=database

# CODE EXECUTION CONFIGURATION
CODE_EXECUTION_ENDPOINT=http://licloud-dify-sandbox:8194
CODE_EXECUTION_API_KEY=Q4793gLSHd1rQGZChcYp5Pcd0ZgQQ2hG4V1UZhqBeckLKiiQ8o++QPNd
CODE_MAX_NUMBER=9223372036854775807
CODE_MIN_NUMBER=-9223372036854775808
CODE_MAX_STRING_LENGTH=80000
TEMPLATE_TRANSFORM_MAX_LENGTH=80000
CODE_MAX_STRING_ARRAY_LENGTH=1000
CODE_MAX_OBJECT_ARRAY_LENGTH=1000
CODE_MAX_NUMBER_ARRAY_LENGTH=1000




# Private model providers configuration
## 托管 azure
HOSTED_AZURE_OPENAI_ENABLED=false
HOSTED_AZURE_OPENAI_QUOTA_LIMIT=1000
HOSTED_AZURE_OPENAI_API_KEY= eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJHMXJuelRGdXF5NUhXd1FjeEg0cm9zWmlUOXhUck5IbSJ9.f6K1OTSN0QTjqF9JOMT3RmbPFew_IIJpCvKH6lfuMVk
HOSTED_AZURE_OPENAI_TRIAL_LLM_MODELS=gpt-4-turbo,gpt-35-turbo,gpt-35-turbo-16k,gpt-4,gpt-4-32k,gpt-4o,gpt-4-vision-preview
HOSTED_AZURE_OPENAI_API_BASE=https://apihub-dify-adaptor.dev.fc.chj.cloud/providers/azure/paths


## licloud
HOSTED_LICLOUD_TRIAL_ENABLED=false
HOSTED_LICLOUD_TRIAL_QUOTA_LIMIT=10000
HOSTED_LICLOUD_TRIAL_APIHUB_KEY=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJHMXJuelRGdXF5NUhXd1FjeEg0cm9zWmlUOXhUck5IbSJ9.f6K1OTSN0QTjqF9JOMT3RmbPFew_IIJpCvKH6lfuMVk
HOSTED_LICLOUD_TRIAL_DBSTORE_KEY=09zLD38B7XvgjrKEirxtHScgSuTpDO8XzQHaF21EGJm8
## HOSTED_LICLOUD_TRIAL_LLM_MODELS=matrix-base-4k,matrix-agent-8k
HOSTED_LICLOUD_TRIAL_EMBEDDING_MODELS=embedding_model_bge_large_zh

## ANTHROPIC
HOSTED_ANTHROPIC_API_KEY=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJHMXJuelRGdXF5NUhXd1FjeEg0cm9zWmlUOXhUck5IbSJ9.f6K1OTSN0QTjqF9JOMT3RmbPFew_IIJpCvKH6lfuMVk
HOSTED_ANTHROPIC_API_BASE=https://apihub-dify-adaptor.dev.fc.chj.cloud/providers/anthropic/paths
HOSTED_ANTHROPIC_TRIAL_LLM_MODELS=claude-3-sonnet-20240229,claude-3-opus-20240229

## TONGYI
HOSTED_TONGYI_API_BASE=https://apihub-dify-adaptor.dev.fc.chj.cloud/providers/tongyi/paths
HOSTED_TONGYI_API_KEY=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJHMXJuelRGdXF5NUhXd1FjeEg0cm9zWmlUOXhUck5IbSJ9.f6K1OTSN0QTjqF9JOMT3RmbPFew_IIJpCvKH6lfuMVk
HOSTED_TONGYI_TRIAL_ENABLED=false
HOSTED_TONGYI_TRIAL_QUOTA_LIMIT=-1
HOSTED_TONGYI_TRIAL_LLM_MODELS=qwen-turbo


# API Tool configuration
API_TOOL_DEFAULT_CONNECT_TIMEOUT=10
API_TOOL_DEFAULT_READ_TIMEOUT=60

# HTTP Node configuration
HTTP_REQUEST_MAX_CONNECT_TIMEOUT=300
HTTP_REQUEST_MAX_READ_TIMEOUT=600
HTTP_REQUEST_MAX_WRITE_TIMEOUT=600
HTTP_REQUEST_NODE_MAX_BINARY_SIZE=10485760 # 10MB
HTTP_REQUEST_NODE_MAX_TEXT_SIZE=1048576 # 1MB

# Log file path
# LOG_FILE=/chj/data/log/licloud-dify-api/licloud-dify-api.log
LOG_LEVEL=DEBUG

# IDAAS configuration
IDAAS_CLIENT_ID=3eOmMCHA8Hk9eNu3hgDvB
IDAAS_CLIENT_SECRET=eyJrdHkiOiJvY3QiLCJraWQiOiJTMGNXUHlYUXRRIiwiYWxnIjoiSFMyNTYiLCJrIjoiblU1c0VmNGcwTFVqZGtTRnhwYTV6WmgxbEdLY1RGRm95S1FSOUk2Y3RIYyJ9
IDAAS_AUTH_URL=https://id-ontest.lixiang.com/api/auth
IDAAS_TOKEN_URL=https://id-ontest.lixiang.com/api/token
IDAAS_USER_INFO_URL=https://li.test.k8s.chj.cloud/licloud-iam-service/v1/users/profile
IDAAS_SERVICE_ID=jIfOD2sWV0SUey4gISJgP
IAM_QUERY_USER_URL= https://li.test.k8s.chj.cloud/licloud-iam-service/v1/users/detail
IAM_USER_INFO_URL = https://li.test.k8s.chj.cloud/licloud-iam-service/v1/users/profile


#INNER API
INNER_API=true
INNER_API_KEY=ZGlmeS1pbm5lci1hcGkta2V5

#MODE
HOSTED_FETCH_APP_TEMPLATES_MODE=builtin

EDITION=CLOUD

# Enterprise Enable
ENTERPRISE_ENABLED="true"
ENTERPRISE_API_URL="https://dify-enterprise-test.inner.chj.cloud/inner/api"
ENTERPRISE_API_SECRET_KEY="LiQNy+YG1JiICfFaGJz45wILBQIlpGIGAhvuRWkwW+8="

ZHIXIAO_BASE_URL=https://cfe-feishu-server-test.chehejia.com

ZHIXIAO_SIGN_SECRET_KEY=9d47346fb18aca34
ZHIXIAO_SIGN_CLIENT_ID=cli_a2efbd090abad013

CELERY_BEAT_SCHEDULER_TYPE=cron
CELERY_BEAT_SCHEDULER_CRON_EXPRESSION="18 2 * * *"

PROMETHEUS_MULTIPROC_DIR=/Users/<USER>/Code/ai/licloud-dify/api/prom

# IDaaS M2M Auth
V4_CLIENT_ID=H3zDKtgxR6FyZTM9MoMEl
V4_CLIENT_SECRET_KEY=eyJrdHkiOiJvY3QiLCJraWQiOiI0ZlotTllLTU93IiwiYWxnIjoiSFMyNTYiLCJrIjoiS3lzck9iUnlQSjB4YVA5c2FsM29CdGx6aV9Jb0hOMEJ4UjA1dGtIQlgtVSJ9
V4_SERVICE_ID=1vDfCRljXShrg4miGrOjgM
V4_SERVICE_SECRET_KEY=eyJrdHkiOiJvY3QiLCJraWQiOiJOYTA4Qlc2ZE93IiwiYWxnIjoiSFMyNTYiLCJrIjoiX3VUWm1nRTl2YkNYQ2RsZGtyT0xXeVc1MzBGMDRlaUI2YXVlU2lvSmF1dyJ9
IDAAS_MGMT_API_SERVICE_ID=XdEjcBLH3AShKophv6p7h
LIID_ENDPOINT=https://liid-ontest.lixiang.com
IDAAS_ENDPOINT=https://id-ontest.lixiang.com/api
LIID_DOMAIN_ID=1ePmlAVpLI6JX37PjEqH6B
M2M_AUTH_ENABLED=false


FEISHU_AUTH_USER_CACHE_TTL=5
FEISHU_AUTH_APP_CACHE_TTL=5

FEISHU_AUTH_APP_TEMP_CACHE_TTL=5

FEISHU_AUTH_EXCLUDED_APPS=["ab567ab","agenta","agent-test-maosijun","agent-version-upgrade-ragauto","agi","ai1-attendance-evaluate-intent","ai1-attendance-evaluate-recm-q","ai1-attendance-evaluate-slot","ai1-attendance-evaluator-1","ai1-attendance-evaluator-intent","ai1-attendance-evaluator-recm-q","ai1-attendance-evaluator-slot","ai1-attendance-timeaccurate","ai1-attendance-time-accurate","ai-assistant-for-chenlc","aibox-agent-workflow","aicodereview","aio-search-rerank","aisales-callsummary-offline-eval-accuracy","aisales-callsummary-offline-eval-readability","aisales-callsummary-offline-eval-simplicity","aisales-userinsights-eval-comprehensive","ai-test","all-in-one-attendance-evaluate-1","all-in-one-attendance-evaluate-2","all-in-one-attendance-evaluate-3","all-in-one-attendance-evaluate-4","all-in-one-attendance-evaluate-5","all-in-one-attendance-evaluate-6","all-in-one-attendance-evaluate-7","all-in-one-attendance-evaluate-8","all-in-one-attendance-evaluate-9","all-in-one-search","all-in-one-search-sub","apollo-check","attendance","attendance-ai","attendence","autotest","autotest-chengzhi","bailian-qwen-max-latest","bingsearch","bingsearch-agent-autotest","bingsearch-workflow-autotest","bingsearch-workflow-chat-autotest","captcha-recognize","case-check-bad-package","case-check-fsd-link-down","cc-aicall-acc-cont","cc-aicall-accuracy","cc-aicall-emotion","cc-aicall-formate","cc-aicall-naturalness-eval","cc-aicall-test","cc-aicall-useguidance","cc-corpus-generate","cc-corpus-generate-part","cc-tele-sale-robot-multichat","ceshi","change-query-agent","chat-assistant","chatbase-version-upgrade-ragauto","chatbot","chat-bot-for-llm","chatbot-for-llm","chatbot-for-webhhok-500","chatbot-for-webhook","chat-demo-for-chenlc","chat-robot-test","check-bugreason","chengzhi-test","classify","claude3-5-test","comment-generation-evaluate","cpd-aftersale-atomic-operation","cr-agent-dev","create-multi-table","cti-reflect","cti-sec","data-evaluation","db-storage-workflow","db-test-workflow","dc-evaluation","dc-muti-dialogue-eval","dc-nl2sql-eval-dev","dc-rag-eval","deepclaude","deepseek-workflow","defect-quality-evaluation","dify-advanced-chat-gpt-35-turbo","dify-agent-claude-35-sonnet","dify-agent-claude-3-opus","dify-agent-claude-3-sonnet","dify-agent-gpt-35-turbo","dify-agent-gpt-35-turbo-16k","dify-agent-gpt-4","dify-agent-gpt-4-32k","dify-agent-gpt-4-mini","dify-agent-gpt-4o","dify-agent-gpt-4-turbo","dify-agent-gpt-4v","dify-agent-image-local-gpt-4-mini","dify-agent-image-local-gpt-4o","dify-agent-image-local-gpt-4-turbo","dify-agent-image-local-gpt-4v","dify-agent-image-remote-gpt-4-mini","dify-agent-image-remote-gpt-4o","dify-agent-image-remote-gpt-4-turbo","dify-agent-image-remote-gpt-4v","dify-agent-matrix-agent-8k","dify-agent-matrix-base-4k","dify-agent-qwen-turbo","dify-chat-claude-35-sonnet","dify-chat-claude-3-opus","dify-chat-claude-3-sonnet","dify-chat-gpt-35-turbo","dify-chat-gpt-35-turbo-16k","dify-chat-gpt-4","dify-chat-gpt-4-32k","dify-chat-gpt-4o","dify-chat-gpt-4o-mini","dify-chat-gpt-4-turbo","dify-chat-gpt-4v","dify-chat-matrix-agent-8k","dify-chat-matrix-base-4k","dify-chat-qwen-turbo","dify-claude-3-opus","dify-claude-3-sonnet","dify-gpt-35-turbo-16k","dify-gpt-4","dify-gpt-4-32k","dify-gpt-4-turbo","dify-image-local-gpt-4-mini","dify-image-local-gpt-4o","dify-image-local-gpt-4-turbo","dify-image-local-gpt-4v","dify-image-remote-gpt-4-mini","dify-image-remote-gpt-4o","dify-image-remote-gpt-4-turbo","dify-image-remote-gpt-4v","dify-matrix-agent-8k","dify-matrix-base-4k","dify-text-autotest-claude-3-sonnet","dify-text-autotest-demo","dify-text-autotest-gpt35-turbo","dify-text-autotest-gpt35-turbo-16k","dify-text-autotest-gpt4","dify-text-autotest-gpt4-32k","dify-text-autotest-gpt4o","dify-text-autotest-gpt4-turbo","dify-text-autotest-gpt4v","dify-text-autotest-matrix-agent-8k","dify-text-autotest-matrix-base-4k","dify-text-autotest-opus-3-sonnet","dify-workflow-claude-35-sonnet","dify-workflow-claude-3-opus","dify-workflow-claude-3-sonnet","dify-workflow-gpt-35-turbo","dify-workflow-gpt-35-turbo-16k","dify-workflow-gpt-4","dify-workflow-gpt-4-32k","dify-workflow-gpt-4o","dify-workflow-gpt-4o-mini","dify-workflow-gpt-4-turbo","dify-workflow-gpt-4v","dify-workflow-matrix-agent-8k","dify-workflow-matrix-base-4k","dify-workflow-qwen-turbo","dip-vdp-analysis-bidsl","dip-vdp-analysis-copilot-intent-detector","dip-vdp-analysis-copilot-querytext-splitter","dip-vdp-analysis-intention-recognition","dip-vdp-appkey-recommend","dip-vdp-field-recommend","dip-vdp-meta-field-recommend","dsv3","emb-finetuning","esdqacodeexplaination","esdqa-easy-test-agent","esdqa-project-test","es-manage-ai","extra-finish-info","fanglong-debut-demo","feishu","feishu-mdsheet","feishu-robot-chat","feishu-robot-chat-workflow","feishu-robot-demo","feishu-robot-text","feishu-send-mess","finance-assistant","finance-assistant-test","finance-chunk","finance-new","finance-wiw","finops-ai","finops-ai-dev","finops-ds-v3","forlowcode","getchatmessage","getchatmessage1","getchatmessage-doubao","get-company-info","hk-eval-acc","hk-eval-acc-taap","hk-eval-cont-taap","hk-eval-naturalness-taap","hk-onlineqa","hk-qtoa","hk-qtoa-prod-taap","hk-qtoa-test-taap","icn-ldm-test","icn-switchenv","if-code-node","if-condiftion-node","if-condiftion-node-for-is-and-is-not","if-condiftion-node-for-is-and-is-not-empty","if-condiftion-node-for-not-contains","if-condiftion-node-for-start-and-end-with","if-file-node","if-list-node","if-list-node-2","if-list-node-3","if-params-node","if-template-node","industrial-artificial-intelligence","iot-log-analysis-v2","itai-workflow","itsm-event-chat-demo","kafka-manage-ai","kaoqin","kaoqin4search","kaoqin-llm-test","kaoqin-test","knowledge","l9-test","lcp-data-cleaner","lcp-data-cleaner-park-fee","li-ai-maintenance-specialist","library-test","licloud-ai-assistant","licloud-jvm-valid","li-comment","li-evaluator","lilog-ops-agent","li-ov-ceshi","llm-router-service","lowbiai","lpai-diagify","majian-dceval","mock-test2","myclip","myclip2","mytest1","network-oncall-agent","network-oncall-agent2","news-gen-demo","openserach","oscar-dify-gpt","otel-concurrent","otel-workflow-microservice","performance","pinyintest","platform-codex-api","pod-ticket-self-service-agent","pod-ticket-self-service-agent-dev","precheck-ci-order","precheck-ci-order-prod","problem-summary","prophet","PSM智能问答助手","qa-intent","qa-knowledge","quality-data-analysis","rag-danlun-eval-template","rag-duolun","rag-duolun-template","rag-eval-acc","rag-fanhua","rag-online-extraction","realmind","redis-ai-manage","reflect","robot-hosted-agent-demo-frequent-questions","robot-hosted-agent-demo-import","saas-agw-oncall-robot-v2","saas-agw-oncall-robot-v2-dev","saos-education-ai-api","saos-education-branch1","saos-education-branch2","saos-education-branch3","saos-education-branch4","saos-fin-dl-agent","saos-jiangsu-kb","saos-rb-common-kb","saos-service-kb","saos-zhejiang-kb","scm-cc-ai-chat","scm-psm-chatbot-1","sde-demo-1","seat-agent-ability","sec-attack","sec-guard","sec-sdlc-llm-prompt-scan","sec-test","shell-gpt-for-chenlc","smart-check-test","smart-check-test-chat-wf","source-test","sre-ai-cvm","sre-ai-cvm-dev","sre-ai-domain","sre-ai-other","ssps","suggestion-for-vulnerability","taap-eval-summary","taap-gen-qap","taap-universal-eval-template","table-value-check","testafdasdfasdf","test-agent-xyfx","test-helper","test-laili-1","test-lisecql","textclean","text-version-upgrade-ragauto","thermal-agent-allinone","thermal-test","todo","tongyiwanx-agent-autotest","tongyiwanx-chat-workflow-autotest","tongyiwanx-workflow-autotest","tpav1","vcos-studio-agent-test","vcos-studio-test-agent","vcos-tools-text-generation","vector-db-manage","von","vrdos-benchmarker-ai","vrdos-benchmarker-attension","vrdos-benchmarker-carplan","vrdos-benchmarker-car-plan","vrdos-benchmarker-doc-summarize","vrdos-benchmarker-feishu-group-agent","vrdos-benchmarker-general","vrdos-benchmarker-general-agent","vrdos-benchmarker-group","vrdos-benchmarker-intention","vrdos-benchmarker-modeling","vrdos-benchmarker-parameter-agent","vrdos-benchmarker-summerize","vrdos-intelli-lab-sardin","weather","wenshengtu","wfl","wifi-ai-as","wifi-file","wireless-ai-app","workflow-nodebuild-auto","workflow-node-code","workflow-node-condition","workflow-node-demo","workflow-node-http","workflow-node-iterate","workflow-node-questions","workflow-node-rag","workflow-node-template","workflow-node-variable","workflow-node-variables","workflow-version-upgrade-autotest","xhs","xiao-test","yiyi-creat-customer-test2","yiyi-intent-type","yuqing","yy-test1-order","zxb-chatworkflow02","zxb-rerank03","zxb-wkflow03","zxb-wrflow","zyt-chatbase-test01","zza1app","测试AI应用站点和API"]