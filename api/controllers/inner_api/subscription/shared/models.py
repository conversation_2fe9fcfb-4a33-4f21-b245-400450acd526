from __future__ import annotations

from dataclasses import dataclass
from enum import Enum
from typing import List, Optional, Dict, Any


class ApprovalStatus(Enum):
    """审批状态枚举"""
    REJECTED = 2
    APPROVED = 5

    @property
    def is_approved(self) -> bool:
        return self == ApprovalStatus.APPROVED

    @property
    def is_rejected(self) -> bool:
        return self == ApprovalStatus.REJECTED


class ProjectType(Enum):
    """项目类型枚举"""
    AI_NATIVE = "AINative"  # 模型
    AI_TOOLS = "AITools"  # 工具
    AI_MCP = "AIMcp"  # MCP

    @property
    def is_model(self) -> bool:
        return self == ProjectType.AI_NATIVE

    @property
    def is_tool(self) -> bool:
        return self == ProjectType.AI_TOOLS

    @property
    def is_mcp(self) -> bool:
        return self == ProjectType.AI_MCP

@dataclass(frozen=True)
class TokenInfo:
    """Token信息"""
    ontest: Optional[str] = None
    prod: Optional[str] = None

    @property
    def has_tokens(self) -> bool:
        return self.ontest is not None or self.prod is not None

    def get_token_for_env(self, target_env: str) -> Optional[str]:
        """根据环境获取对应token"""
        if target_env == "prod":
            return self.prod
        elif target_env == "ontest":
            return self.ontest
        return None


@dataclass(frozen=True)
class ProjectInfo:
    """项目信息"""
    code: str
    name: str
    project_type: ProjectType
    description: str
    tags: List[str]

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> ProjectInfo:
        return cls(
            code=data['projectCode'],
            name=data['projectName'],
            project_type=ProjectType(data['projectType']),
            description=data.get('desc', ''),
            tags=data.get('tags', [])
        )
    @property
    def is_model_project(self) -> bool:
        """是否为模型项目"""
        return self.project_type.is_model

    @property
    def is_tool_project(self) -> bool:
        """是否为工具项目"""
        return self.project_type.is_tool

    @property
    def is_mcp_project(self) -> bool:
        """是否为MCP项目"""
        return self.project_type.is_mcp


@dataclass(frozen=True)
class SubscriptionData:
    """订阅数据"""
    audit_id: int
    app: str
    projects: List[ProjectInfo]
    process_id: str
    app_type: str
    tenant_id: str
    status: ApprovalStatus
    username: str
    tokens: TokenInfo

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> SubscriptionData:
        projects = [ProjectInfo.from_dict(p) for p in data['projects']]
        tokens = cls._extract_tokens(data['token'])

        return cls(
            audit_id=data['auditId'],
            app=data['app'],
            projects=projects,
            process_id=data['processId'],
            app_type=data['appType'],
            tenant_id=data['tenantId'],
            status=ApprovalStatus(data['status']),
            username=data['username'],
            tokens=tokens
        )

    @staticmethod
    def _extract_tokens(token_list: List[Dict[str, str]]) -> TokenInfo:
        tokens = {}
        for token_dict in token_list:
            tokens.update(token_dict)
        return TokenInfo(
            ontest=tokens.get('ontest'),
            prod=tokens.get('prod')
        )

    @property
    def project_codes(self) -> List[str]:
        return [project.code for project in self.projects]


@dataclass(frozen=True)
class SubscriptionEvent:
    """订阅事件"""
    data: SubscriptionData
    subject: str
    specversion: str
    source: str
    id: str
    time: str
    type: str
    dataschema: str