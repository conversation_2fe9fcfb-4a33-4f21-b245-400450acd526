from typing import Optional, Dict, Any, Callable, List, Union
import logging

from .models import SubscriptionEvent, SubscriptionData, ProjectType
from cloudevents.http import CloudEvent
logger = logging.getLogger(__name__)


class SubscriptionEventProcessor:
    """通用订阅事件处理器 - 负责序列化、日志、路由"""

    def __init__(self,
                 approved_handler: Callable[[SubscriptionEvent], Dict[str, Any]] = None,
                 rejected_handler: Callable[[SubscriptionEvent], Dict[str, Any]] = None,
                 app_type_filter: str = None,
                 project_type_filter: Union[ProjectType, List[ProjectType]] = None):
        """
        初始化处理器

        Args:
            approved_handler: 审核通过的处理函数
            rejected_handler: 审核拒绝的处理函数
            app_type_filter: 应用类型/调用方类型过滤器，如 "LiCloudAI"
            project_type_filter: 项目类型过滤器，可以是单个类型或类型列表; 示例：AINative（模型），AITools（工具），AIMcp（mcp）
        """
        self.approved_handler = approved_handler
        self.rejected_handler = rejected_handler
        self.app_type_filter = app_type_filter

        # 处理项目类型过滤器
        if project_type_filter is None:
            self.project_type_filter = None
        elif isinstance(project_type_filter, ProjectType):
            self.project_type_filter = [project_type_filter]
        else:
            self.project_type_filter = project_type_filter

    def parse_event(self, raw_event: CloudEvent) -> Optional[SubscriptionEvent]:
        """解析原始事件为结构化对象"""
        event_id = raw_event.get('id', 'unknown')

        # 过滤事件类型
        event_type = raw_event.get("type")
        if event_type != "ai.sub.result":
            logger.debug(f"[{event_id}] Event type '{event_type}' filtered out")
            return None

        # 过滤空数据
        event_data = raw_event.data
        if not event_data:
            logger.warning(f"[{event_id}] Event data is empty")
            return None

        # 过滤应用类型
        if self.app_type_filter and event_data.get("appType") != self.app_type_filter:
            logger.debug(f"[{event_id}] AppType '{event_data.get('appType')}' filtered out, "
                         f"expected: {self.app_type_filter}")
            return None

        try:
            data = SubscriptionData.from_dict(event_data)

            # 过滤项目类型
            if self.project_type_filter:
                filtered_projects = [
                    project for project in data.projects
                    if project.project_type in self.project_type_filter
                ]

                if not filtered_projects:
                    logger.debug(f"[{event_id}] No projects match project type filter: "
                                 f"{[ptype.value for ptype in self.project_type_filter]}")
                    return None

                # 创建新的SubscriptionData，只包含符合条件的Project
                data = SubscriptionData(
                    audit_id=data.audit_id,
                    app=data.app,
                    projects=filtered_projects,
                    process_id=data.process_id,
                    app_type=data.app_type,
                    tenant_id=data.tenant_id,
                    status=data.status,
                    username=data.username,
                    tokens=data.tokens
                )

                logger.debug(f"[{event_id}] Filtered projects by type, remaining: {len(filtered_projects)}")

            event = SubscriptionEvent(
                data=data,
                subject=raw_event['subject'],
                specversion=raw_event['specversion'],
                source=raw_event['source'],
                id=raw_event['id'],
                time=raw_event['time'],
                type=raw_event['type'],
                dataschema=raw_event['dataschema']
            )
            logger.debug(f"[{event_id}] Event parsed successfully")
            return event

        except (KeyError, ValueError, TypeError) as e:
            logger.error(f"[{event_id}] Failed to parse event data: {e}")
            return None

    def process(self, event: SubscriptionEvent) -> Dict[str, Any]:
        """处理订阅事件"""
        event_id = event.id
        logger.info(f"[{event_id}] 🔄 Processing subscription event for user {event.data.username}")

        try:
            # 记录事件详情
            self._log_event_details(event)

            try:
                # 记录事件详情
                self._log_event_details(event)

                # 根据状态路由处理
                if event.data.status.is_approved:
                    if self.approved_handler:
                        logger.info(f"[{event_id}] 🎯 Routing to approved handler")
                        result = self.approved_handler(event)
                        logger.info(f"[{event_id}] ✅ Approved handler completed successfully")
                        return result
                    else:
                        logger.warning(f"[{event_id}] ❓ No approved handler configured")
                        return self._default_response(event, "No approved handler")

                elif event.data.status.is_rejected:
                    if self.rejected_handler:
                        logger.info(f"[{event_id}] 🎯 Routing to rejected handler")
                        result = self.rejected_handler(event)
                        logger.info(f"[{event_id}] ✅ Rejected handler completed successfully")
                        return result
                    else:
                        logger.debug(f"[{event_id}] Status is rejected, skipping processing")
                        return self._default_response(event, "Status rejected")
                else:
                    logger.warning(f"[{event_id}] ❓ Unknown approval status: {event.data.status}")
                    return self._default_response(event, f"Unknown status: {event.data.status}")

            except Exception as e:
                logger.error(f"[{event_id}] 💥 Error in event processing: {e}")
                return self._default_response(event, f"Processing error: {str(e)}")

        except Exception as e:
            logger.error(f"[{event_id}] 💥 Error in event processing: {e}")
            return self._default_response(event, f"Processing error: {str(e)}")

    def _log_event_details(self, event: SubscriptionEvent) -> None:
        """记录事件详细信息"""
        event_id = event.id
        data = event.data

        logger.info(f"[{event_id}] 📋 Event Details:")
        logger.info(f"[{event_id}]    • Source: {event.source}")
        logger.info(f"[{event_id}]    • Time: {event.time}")
        logger.info(f"[{event_id}]    • Audit ID: {data.audit_id}")
        logger.info(f"[{event_id}]    • Process ID: {data.process_id}")
        logger.info(f"[{event_id}]    • App: {data.app} ({data.app_type})")
        logger.info(f"[{event_id}]    • User: {data.username}@{data.tenant_id}")
        logger.info(f"[{event_id}]    • Status: {data.status.name}")
        logger.info(f"[{event_id}]    • Projects: {len(data.projects)} items")

        # 按项目类型分组显示
        project_types = {}
        for project in data.projects:
            ptype = project.project_type.value
            if ptype not in project_types:
                project_types[ptype] = []
            project_types[ptype].append(project)

        for ptype, projects in project_types.items():
            logger.info(f"[{event_id}]      {ptype} ({len(projects)} items):")
            for i, project in enumerate(projects, 1):
                tags_str = f", tags: {project.tags}" if project.tags else ""
                logger.info(f"[{event_id}]        {i}. {project.name} ({project.code}){tags_str}")
                if project.description:
                    logger.info(f"[{event_id}]           描述: {project.description}")

        if data.tokens.has_tokens:
            logger.info(f"[{event_id}]    • Tokens: ontest={'✓' if data.tokens.ontest else '✗'}, "
                        f"prod={'✓' if data.tokens.prod else '✗'}")

    def _default_response(self, event: SubscriptionEvent, message: str) -> Dict[str, Any]:
        """默认响应"""
        return {
            "status": "unhandled",
            "event_id": event.id,
            "audit_id": event.data.audit_id,
            "message": message
        }