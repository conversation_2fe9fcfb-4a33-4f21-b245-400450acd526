"""modify_datasets_permission_default

Revision ID: 533c1888af97
Revises: 53bf8af60645
Create Date: 2024-08-27 06:13:58.762375

"""
from alembic import op
import models as models
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = '533c1888af97'
down_revision = '53bf8af60645'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('datasets', schema=None) as batch_op:
        batch_op.alter_column('permission', server_default=sa.text("'all_team_members'::character varying"))

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('datasets', schema=None) as batch_op:
        batch_op.alter_column('permission', server_default=sa.text("'only_me'::character varying"))

    # ### end Alembic commands ###
