import datetime
import json
import time
import logging
import click

import app
from werkzeug.exceptions import NotFound
from configs import dify_config
from core.utils.document_helpers import has_user_authorized_feishu
from extensions.ext_database import db
from models.dataset import Dataset, Document
from tasks.document_indexing_update_task import document_indexing_update_task
from core.utils.feishu_utils import Feishu
from flask_login import current_user


@app.celery.task(queue="dataset")
def sync_feishu_documents_task():
    logging.info(click.style("[Feishu] Start syncing Feishu documents.", fg="green"))
    start_time = time.perf_counter()
    sync_qps = int(dify_config.SYNC_FEISHU_DOCUMENT_QPS)
    page = 1

    while True:
        datasets = get_datasets(page)
        if not datasets:
            break
        for dataset in datasets:
            sync_documents(dataset, sync_qps)
        page += 1

    log_duration(start_time)


def get_datasets(page):
    try:
        logging.info(click.style(f"[<PERSON><PERSON><PERSON>] Fetching datasets, page: {page}", fg="blue"))
        return db.session.query(Dataset).paginate(page=page, per_page=50).items
    except NotFound:
        logging.info(click.style("[Feishu] No more datasets found.", fg="yellow"))
        return None


def sync_documents(dataset, sync_qps):
    documents = db.session.query(Document).filter(
        Document.dataset_id == dataset.id,
        Document.data_source_type == "feishu_import"
    ).all()

    for document in throttle_documents(documents, sync_qps):

        if document.data_source_info_dict.get("auto_sync_enabled", False):
            try:
                should_update = check_document_needs_update(document)

                if should_update:

                    # Log the start of document syncing
                    logging.info(click.style(f"[Feishu] Syncing document ID: {document.id}", fg="cyan"))

                    # Check if the user is authorized to view the Feishu document
                    if not has_user_authorized_feishu(document):
                        # Log the authorization error and update the document error field
                        document.error = "User does not have permission to view this Feishu document"
                        db.session.commit()
                        logging.warning(
                            click.style(f"[Feishu] User not authorized for document ID: {document.id}", fg="yellow"))
                        continue

                    # Update document indexing task
                    document_indexing_update_task(dataset.id, document.id)
            except Exception as e:
                logging.error(click.style(f"[Feishu] Error syncing document {document.id}: {str(e)}", fg="red"))

        logging.info(f"Auto sync enabled: {document.data_source_info_dict.get('auto_sync_enabled', False)}")

def throttle_documents(documents, sync_qps):
    documents_processed = 0
    for document in documents:
        yield document
        documents_processed += 1
        if documents_processed >= sync_qps:
            logging.info(click.style("[Feishu] Rate limit reached, sleeping for 1 second.", fg="yellow"))
            time.sleep(1)
            documents_processed = 0


def log_duration(start_time):
    elapsed_time = time.perf_counter() - start_time
    logging.info(click.style(f"[Feishu] Sync completed in {elapsed_time:.2f} seconds.", fg="green"))


def document_update_for_document_id(dataset_id, document_id):
    try:
        dataset = db.session.query(Dataset).filter(Dataset.id == dataset_id).first()

        documents = db.session.query(Document).filter(
            Document.dataset_id == dataset.id,
            Document.data_source_type == "feishu_import",
            Document.id == document_id
        ).all()

        try:
            for document in documents:
                # Log the start of document syncing
                logging.info(click.style(f"[Feishu] Syncing document ID: {document.id}", fg="cyan"))

                # Check if the user is authorized to view the Feishu document
                if not has_user_authorized_feishu(document):
                    # Log the authorization error and update the document error field
                    document.error = "User does not have permission to view this Feishu document"
                    db.session.commit()
                    logging.warning(
                        click.style(f"[Feishu] User not authorized for document ID: {document.id}",
                                    fg="yellow"))
                # Update document indexing task
                document_indexing_update_task(dataset.id, document.id)
        except Exception as e:
            logging.error(click.style(f"[Feishu] Error syncing document {document.id}: {str(e)}", fg="red"))

            logging.info(f"Auto sync enabled: {document.data_source_info_dict.get('auto_sync_enabled', False)}")

    except Exception as e:
        logging.error(f"documents_update_for_dataset_id: {e}")
        return e


def documents_update_for_dataset_id(dataset_id):
    try:
        sync_qps = int(dify_config.SYNC_FEISHU_DOCUMENT_QPS)

        dataset = db.session.query(Dataset).filter(Dataset.id==dataset_id).first()

        sync_documents(dataset=dataset, sync_qps=sync_qps)

    except Exception as e:
        logging.error(f"documents_update_for_dataset_id: {e}")
        return e


def check_document_needs_update(document):
    try:
        data_source_info_dict = document.data_source_info_dict

        db_latest_modify_time = get_db_latest_modify_time(data_source_info=data_source_info_dict)

        feishu_latest_modify_time, feishu_metas = get_feishu_metas_and_latest_modify_time(data_source_info_dict)

        if not feishu_latest_modify_time:
            return False

        if not db_latest_modify_time:
            logging.info(f"数据库未查询到 data_source_info:{data_source_info_dict}")

            update_document_metadata(document=document, feishu_metas=feishu_metas)

            return True

        if compare_feishu_time(db_latest_modify_time, feishu_latest_modify_time):
            update_document_metadata(document=document, feishu_metas=feishu_metas)

            return True

        return False

    except Exception as e:
        logging.error(f"check_document_needs_update ERROR: {e}")
        return False


def get_db_latest_modify_time(data_source_info):
    db_latest_modify_time = data_source_info.get("feishu_metas")

    if db_latest_modify_time:
        db_latest_modify_time = db_latest_modify_time.get("latest_modify_time")

    return db_latest_modify_time


def get_feishu_metas_and_latest_modify_time(data_source_info_dict):
    try:
        user_id = data_source_info_dict.get("user_id")
        if not user_id:
            logging.info(f"数据库未查询到用户信息 user_id: {user_id} \n")
            return None, None

        url = data_source_info_dict.get("url")

        feishu_data = Feishu.get_instance().get_file_meta(user_id=user_id, documet_url_list=url)

        metas = feishu_data.get("metas")

        if not metas or not metas[0].get("latest_modify_time"):
            logging.info(f"Feishu通用服务未查询到 url: {url} \n"
                         f"feishu_data: {feishu_data} \n")
            return None, None

        feishu_metas = metas[0]

        # 去除冗余信息
        if feishu_metas.get("children"):
            feishu_metas["children"] = []

        feishu_latest_modify_time = feishu_metas.get("latest_modify_time")

        logging.info(f"Feishu通用服务 url: {url} \n"
                     f"feishu_data: {feishu_data} \n")

        return feishu_latest_modify_time, feishu_metas

    except Exception as e:
        logging.error(f"get_feishu_metas_and_latest_modify_time ERROR: {e}")
        return None, None


def update_document_metadata(document, feishu_metas):
    try:
        data_source_info_dict = document.data_source_info_dict

        data_source_info_dict["feishu_metas"] = feishu_metas

        _, data_source_info_dict["feishu_metas"]["latest_modify_iso8601time"] = timestamp_to_iso8601(
            feishu_metas.get("latest_modify_time"))

        document.data_source_info = json.dumps(data_source_info_dict)
        document.name = feishu_metas.get("title")
        db.session.commit()

    except Exception as e:
        logging.error(f"update_document_meta ERROR: {e}")
        db.session.rollback()


def compare_feishu_time(db_latest_modify_time, feishu_latest_modify_time):
    try:
        db_latest_modify_time, db_latest_modify_time_8601 = timestamp_to_iso8601(db_latest_modify_time)

        feishu_latest_modify_time, feishu_latest_modify_time_8601 = timestamp_to_iso8601(feishu_latest_modify_time)

        logging.info(f"db时间:{db_latest_modify_time_8601} feishu时间:{feishu_latest_modify_time_8601}")

        return feishu_latest_modify_time > db_latest_modify_time

    except Exception as e:
        logging.info(f"ERROR {e}：db时间:{db_latest_modify_time_8601} feishu时间:{feishu_latest_modify_time_8601}")
        return False


def timestamp_to_iso8601(timestamp):
    dt_local = datetime.datetime.fromtimestamp(float(timestamp))
    iso_8601_local_str = dt_local.astimezone().isoformat()

    return dt_local, iso_8601_local_str


def iso8601_to_datetime(iso_str):
    if iso_str.endswith("Z"):
        iso_str = iso_str.replace("Z", "+00:00")
    return datetime.datetime.fromisoformat(iso_str)
