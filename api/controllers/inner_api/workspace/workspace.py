import logging
import json

from cloudevents.http import from_http
from flask import request
from flask_restful import Resource, fields, reqparse, marshal

from controllers.console.wraps import setup_required
from controllers.inner_api import api
from controllers.inner_api.wraps import inner_api_only
from events.tenant_event import tenant_was_created
from libs.helper import TimestampField
from models.account import Account
from services.account_service import TenantService, AccountService


tenants_fields = {
    "id": fields.String,
    "name": fields.String,
    "plan": fields.String,
    "status": fields.String,
    "created_at": TimestampField,
    "current": fields.Boolean,
}


class EnterpriseWorkspace(Resource):
    @setup_required
    @inner_api_only
    def post(self):
        try:
            parser = reqparse.RequestParser()
            parser.add_argument("name", type=str, required=True, location="json")
            parser.add_argument("owner_email", type=str, required=True, location="json")
            parser.add_argument("admin_email", type=str, location='json')
            parser.add_argument("custom_config", type=dict, location="json")
            args = parser.parse_args()

            logging.info(f"EnterpriseWorkspace args info:{args}")

            # account = Account.query.filter_by(email=args["owner_email"]).first()
            account = AccountService.find_user_for_email(args["owner_email"])

            if not account:
                return {"message": "Account not found"}, 404

            tenant = TenantService.create_tenant(args["name"], is_from_dashboard=True)
            tenant.custom_config_dict = args["custom_config"]
            TenantService.create_tenant_member(tenant, account, role="owner")

            if args["admin_email"]:
                # admin = Account.query.filter_by(email=args["admin_email"]).first()
                admin = AccountService.find_user_for_email(args["admin_email"])
                TenantService.create_tenant_member(tenant, admin, role="admin")

            tenant_was_created.send(tenant)

            return {
                "message": "enterprise workspace created.",
                "tenant_id": tenant.id
            }

        except Exception as e:
            logging.error(f"EnterpriseWorkspace.Post Error: {str(e)}")
            return {"message":f"ERROR:{e}"},404



class EnterpriseWorkspaceEvent(Resource):
    @setup_required
    @inner_api_only
    def post(self):
        event = from_http(request.headers, request.get_data())

        if event.get_attributes()["type"] == "cloud.chj.li.oam.app.deleted":
            app = event.get_data()["data"]
            if app and app["appType"] == "dify" and app["extensions"]["difyWorkspace"]["id"]:
                TenantService.delete_tenant(app["extensions"]["difyWorkspace"]["id"])
                return {
                    "message": "workspace deleted"
                }, 200

        return "", 200


class EnterpriseTenantListApi(Resource):
    @setup_required
    @inner_api_only
    def get(self):
        user_id = request.args.get("user_id", default=None, type=str)
        current_user = AccountService.get_account_by_name(user_id)
        tenants = TenantService.get_join_tenants(current_user)
        return {"workspaces": marshal(tenants, tenants_fields)}, 200

class EnterpriseWorkspaceNoOwnerEmail(Resource):
    @setup_required
    @inner_api_only
    def post(self):
        parser = reqparse.RequestParser()
        parser.add_argument("name", type=str, required=True, location="json")
        args = parser.parse_args()

        tenant = TenantService.create_tenant(args["name"], is_from_dashboard=True)

        tenant_was_created.send(tenant)

        resp = {
            "id": tenant.id,
            "name": tenant.name,
            "encrypt_public_key": tenant.encrypt_public_key,
            "plan": tenant.plan,
            "status": tenant.status,
            "custom_config": json.loads(tenant.custom_config) if tenant.custom_config else {},
            "created_at": tenant.created_at.isoformat() if tenant.created_at else None,
            "updated_at": tenant.updated_at.isoformat() if tenant.updated_at else None,
        }

        return {
            "message": "enterprise workspace created.",
            "tenant": resp,
        }



api.add_resource(EnterpriseTenantListApi, "/enterprise/workspaces")
api.add_resource(EnterpriseWorkspace, "/enterprise/workspace")
api.add_resource(EnterpriseWorkspaceEvent, "/enterprise/workspace/remove")
api.add_resource(EnterpriseWorkspaceNoOwnerEmail, "/enterprise/workspace/ownerless")
