import io
from copy import deepcopy
from typing import Optional

from flask import send_file
from flask_restful import Resource
from pydantic import BaseModel

from configs import dify_config
from controllers.console import api
from controllers.console.wraps import setup_required, account_initialization_required
from core.agent.strategy.builtin_agent_strategy_provider import BuiltinAgentStrategyProviderController
from core.agent.strategy.entities import AgentStrategyParameter, AgentProviderIdentity, AgentStrategyIdentity, \
    AgentFeature
from core.tools.entities.common_entities import I18nObject
from libs.login import login_required
from services.agent_strategy_provider_service import AgentStrategyProviderService


class StrategyDetail(BaseModel):
    identity: AgentStrategyIdentity
    parameters: list[AgentStrategyParameter]
    description: I18nObject
    output_schema: Optional[dict]
    features: list[str]


class StrategyDeclaration(BaseModel):
    identity: AgentProviderIdentity
    plugin_id: str = "langgenius/agent/agent"
    strategies: list[StrategyDetail]
    label: Optional[I18nObject] = None


class StrategyPluginDetail(BaseModel):
    provider: str
    plugin_unique_identifier: str
    plugin_id: str
    declaration: StrategyDeclaration


def transform_to_response(provider: BuiltinAgentStrategyProviderController) -> StrategyPluginDetail:
    plugin_unique_identifier = "langgenius/agent:0.0.14@26958a0e80a10655ce73812bdb7c35a66ce7b16f5ac346d298bda17ff85efd1e"
    plugin_id = "langgenius/agent"
    strategies = []

    for strategy in provider.strategies:

        strategy_detail = StrategyDetail(
            identity=deepcopy(strategy.identity),
            parameters=strategy.parameters,
            description=strategy.description,
            output_schema=None,
            features=strategy.features,
        )
        strategy_detail.identity.icon = f"{dify_config.CONSOLE_API_URL}/console/api/workspaces/current/agent-provider/{provider.identity.provider}/icon"

        strategies.append(strategy_detail)

    result = StrategyPluginDetail(
        provider=provider.identity.provider,
        plugin_unique_identifier=plugin_unique_identifier,
        plugin_id=plugin_id,
        declaration=StrategyDeclaration(
            identity=deepcopy(provider.identity),
            plugin_id=plugin_id,
            strategies=strategies,
            label=provider.identity.label,
        )
    )
    result.declaration.identity.icon = f"{dify_config.CONSOLE_API_URL}/console/api/workspaces/current/agent-provider/{provider.identity.provider}/icon"
    return result


class AgentProviderListApi(Resource):
    @setup_required
    @login_required
    @account_initialization_required
    def get(self):
        providers = AgentStrategyProviderService.get_provider_list()
        return [transform_to_response(x).model_dump() for x in providers]


class AgentProviderApi(Resource):
    @setup_required
    @login_required
    @account_initialization_required
    def get(self, provider: str):
        providers = AgentStrategyProviderService.get_provider_list()
        for _provider in providers:
            if _provider.identity.provider == provider:
                return transform_to_response(_provider).model_dump()

        return {"error": "Provider not found"}, 404


class AgentProviderIconApi(Resource):
    @setup_required
    def get(self, provider):
        icon_bytes, mimetype = AgentStrategyProviderService.get_provider_icon(provider)
        icon_cache_max_age = dify_config.TOOL_ICON_CACHE_MAX_AGE
        return send_file(io.BytesIO(icon_bytes), mimetype=mimetype, max_age=icon_cache_max_age)

api.add_resource(AgentProviderListApi, "/workspaces/current/agent-providers")
api.add_resource(AgentProviderApi, "/workspaces/current/agent-provider/<string:provider>")
api.add_resource(AgentProviderIconApi, "/workspaces/current/agent-provider/<string:provider>/icon")