import json
import logging

from cloudevents.http import from_http
from flask import request
from flask_restful import Resource

from controllers.inner_api import api
from controllers.inner_api.wraps import inner_api_only
from models import Tenant, db
from services.account_service import TenantService
from services.licloud_event_service import LiCloudEventService

logger = logging.getLogger(__name__)

class LicloudEventProcessor(Resource):

    @inner_api_only
    def post(self):
        event = from_http(request.headers, request.get_data())
        source = event.get_attributes()['source']
        event_type = event.get_attributes()['type']
        event_id = event.get_attributes()["id"]
        logger.debug(f"Received event, source = {source}, type = {event_type}, id = {event_id}")

        if event_type == 'cloud.chj.li.iam.user.group.delete':
            # 处理用户从组中移除事件
            LiCloudEventService().tenant_group_member_leave(event)
            return "", 200
        elif event_type == 'cloud.chj.li.oam.app.transfer.success':
            # 处理应用转移成功事件
            data = event.get_data()
            licloud_app_name = data['appName'] if 'appName' in data else None
            old_tenant_id = data['oldTenantId'] if 'oldTenantId' in data else None
            new_tenant_id = data['newTenantId'] if 'newTenantId' in data else None
            if not licloud_app_name or not old_tenant_id or not new_tenant_id:
                logger.warning(f"App transform failed, missing required fields, licloud_app_name = {licloud_app_name}, old_tenant_id = {old_tenant_id}, new_tenant_id = {new_tenant_id}")
                return "", 400

            tenant = TenantService.get_tenant_by_custom_config({
                "licloud_tenant_id": old_tenant_id,
                "licloud_app_name": licloud_app_name
            })

            if (tenant and tenant.custom_config_dict
                    and tenant.custom_config_dict["licloud_tenant_id"] == old_tenant_id
                    and tenant.custom_config_dict["licloud_app_name"] == licloud_app_name):
                new_custom_config = tenant.custom_config_dict.copy()
                new_custom_config["licloud_tenant_id"] = new_tenant_id
                tenant.custom_config_dict = new_custom_config
                db.session.add(tenant)
                db.session.commit()
                logger.info(f"App transform success, licloud_tenant_id = {old_tenant_id}, app_name = {licloud_app_name}, new_tenant_id = {new_tenant_id}")
            else:
                logger.info(f"App transform failed, tenant not found, licloud_tenant_id = {old_tenant_id}, app_name = {licloud_app_name}")

            return "", 200
        else:
            logger.info(f"Received unsupported event: source = {source}, type = {event_type}")
            return "", 200


api.add_resource(LicloudEventProcessor, '/enterprise/licloud-events')