import json
import logging
from collections.abc import Generator, Mapping, Sequence
from typing import Any, Optional, cast

from sqlalchemy import select
from sqlalchemy.orm import Session

from configs import dify_config
from core.agent.entities import AgentInvokeMessage
from core.agent.strategy.builtin_agent_strategy_manager import BuiltinAgentStrategyManager
from core.agent.strategy.entities import AgentStrategyParameter
from core.agent.strategy.factory import get_agent_strategy
from core.entities.parameter_entities import CommonParameterType
from core.file import File, FileTransferMethod
from core.memory.token_buffer_memory import TokenBufferMemory
from core.model_manager import ModelInstance, ModelManager
from core.model_runtime.entities.model_entities import AIModelEntity, ModelType
from core.provider_manager import ProviderManager
from core.tools.entities.tool_entities import ToolParameter, ToolProviderType
from core.tools.tool_manager import ToolManager
from core.tools.utils.message_transformer import ToolFileMessageTransformer
from core.variables.segments import StringSegment
from core.workflow.entities.node_entities import <PERSON>deRun<PERSON><PERSON>ult, NodeRunMeta<PERSON><PERSON>ey
from core.workflow.entities.variable_pool import VariablePool
from core.workflow.enums import <PERSON>Varia<PERSON><PERSON>ey
from core.workflow.graph_engine.entities.event import AgentLogEvent
from core.workflow.nodes.agent.agent_strategy import WorkflowAgentStrategy
from core.workflow.nodes.agent.entities import AgentNodeData, AgentOldVersionModelFeatures, ParamsAutoGenerated
from core.workflow.nodes.base.entities import BaseNodeData
from core.workflow.nodes.enums import NodeType
from core.workflow.nodes.event.event import RunCompletedEvent, RunStreamChunkEvent
from core.workflow.nodes.tool.entities import ToolEntity
from core.workflow.nodes.tool.exc import ToolFileError
from core.workflow.nodes.tool.tool_node import ToolNode
from core.workflow.utils.variable_template_parser import VariableTemplateParser
from extensions.ext_database import db
from factories import file_factory
from models import ToolFile
from models.model import Conversation
from models.workflow import WorkflowNodeExecutionStatus
from services.tools.builtin_tools_manage_service import BuiltinToolManageService

logger = logging.getLogger(__name__)


class AgentNode(ToolNode):
    """
    Agent Node
    """

    _node_data_cls = AgentNodeData  # type: ignore
    _node_type = NodeType.AGENT

    @classmethod
    def version(cls) -> str:
        return "1"

    def _run(self) -> Generator:
        """
        Run the agent node
        """
        node_data = cast(AgentNodeData, self.node_data)

        try:
            strategy_core = get_agent_strategy(
                agent_strategy_provider_name=node_data.agent_strategy_provider_name,
                agent_strategy_name=node_data.agent_strategy_name,
            )
            strategy = WorkflowAgentStrategy(
                node=self,
                declaration=strategy_core,
            )
        except Exception as e:
            yield RunCompletedEvent(
                run_result=NodeRunResult(
                    status=WorkflowNodeExecutionStatus.FAILED,
                    inputs={},
                    error=f"Failed to get agent strategy: {str(e)}",
                )
            )
            return

        agent_parameters = strategy.get_parameters()

        # get parameters
        parameters = self._generate_agent_parameters(
            agent_parameters=agent_parameters,
            variable_pool=self.graph_runtime_state.variable_pool,
            node_data=node_data,
        )
        parameters_for_log = self._generate_agent_parameters(
            agent_parameters=agent_parameters,
            variable_pool=self.graph_runtime_state.variable_pool,
            node_data=node_data,
            for_log=True,
        )

        # get conversation id
        conversation_id = self.graph_runtime_state.variable_pool.get(["sys", SystemVariableKey.CONVERSATION_ID])

        try:
            message_stream = strategy.invoke(
                params=parameters,
                conversation_id=conversation_id.text if conversation_id else None,
            )
        except Exception as e:
            yield RunCompletedEvent(
                run_result=NodeRunResult(
                    status=WorkflowNodeExecutionStatus.FAILED,
                    inputs=parameters_for_log,
                    error=f"Failed to invoke agent: {str(e)}",
                )
            )
            return

        try:
            # convert tool messages

            yield from self._transform_message(
                message_stream,
                {
                    "icon": self.agent_strategy_icon,
                    "agent_strategy": cast(AgentNodeData, self.node_data).agent_strategy_name,
                },
                parameters_for_log,
            )
        except Exception as e:
            logger.error(f"Failed to transform agent message: {str(e)}", e)
            yield RunCompletedEvent(
                run_result=NodeRunResult(
                    status=WorkflowNodeExecutionStatus.FAILED,
                    inputs=parameters_for_log,
                    error=f"Failed to transform agent message: {str(e)}",
                )
            )

    def _generate_agent_parameters(
        self,
        *,
        agent_parameters: Sequence[AgentStrategyParameter],
        variable_pool: VariablePool,
        node_data: AgentNodeData,
        for_log: bool = False,
    ) -> dict[str, Any]:
        """
        Generate parameters based on the given tool parameters, variable pool, and node data.

        Args:
            agent_parameters (Sequence[AgentParameter]): The list of agent parameters.
            variable_pool (VariablePool): The variable pool containing the variables.
            node_data (AgentNodeData): The data associated with the agent node.

        Returns:
            Mapping[str, Any]: A dictionary containing the generated parameters.

        """
        agent_parameters_dictionary = {parameter.name: parameter for parameter in agent_parameters}

        result: dict[str, Any] = {}
        for parameter_name in node_data.agent_parameters:
            parameter = agent_parameters_dictionary.get(parameter_name)
            if not parameter:
                result[parameter_name] = None
                continue
            agent_input = node_data.agent_parameters[parameter_name]
            if agent_input.type == "variable":
                variable = variable_pool.get(agent_input.value)  # type: ignore
                if variable is None:
                    raise ValueError(f"Variable {agent_input.value} does not exist")
                parameter_value = variable.value
            elif agent_input.type in {"mixed", "constant"}:
                # variable_pool.convert_template expects a string template,
                # but if passing a dict, convert to JSON string first before rendering
                try:
                    if not isinstance(agent_input.value, str):
                        parameter_value = json.dumps(agent_input.value, ensure_ascii=False)
                    else:
                        parameter_value = str(agent_input.value)
                except TypeError:
                    parameter_value = str(agent_input.value)
                segment_group = variable_pool.convert_template(parameter_value)
                parameter_value = segment_group.log if for_log else segment_group.text
                # variable_pool.convert_template returns a string,
                # so we need to convert it back to a dictionary
                try:
                    if not isinstance(agent_input.value, str):
                        parameter_value = json.loads(parameter_value)
                except json.JSONDecodeError:
                    parameter_value = parameter_value
            else:
                raise ValueError(f"Unknown agent input type '{agent_input.type}'")
            value = parameter_value
            if parameter.type == "array[tools]":
                value = cast(list[dict[str, Any]], value)
                value = [tool for tool in value if tool.get("enabled", False)]
                for tool in value:
                    if "schemas" in tool:
                        tool.pop("schemas")
                    parameters = tool.get("parameters", {})
                    if all(isinstance(v, dict) for _, v in parameters.items()):
                        params = {}
                        for key, param in parameters.items():
                            if param.get("auto", ParamsAutoGenerated.OPEN.value) == ParamsAutoGenerated.CLOSE.value:
                                value_param = param.get("value", {})
                                if value_param.get("type") == "variable":
                                    variable = variable_pool.get(value_param.get("value"))
                                    logger.debug("tool variable parameter: %s", variable)
                                    if variable is None:
                                        raise ValueError(f"Variable {param.get('value')} does not exist")
                                    value_param = {"value": variable.text}
                                else:
                                    value_param = param.get("value", {})
                                params[key] = value_param.get("value", "") if value_param is not None else None
                            else:
                                params[key] = None
                        parameters = params
                    tool["settings"] = {k: v.get("value", None) for k, v in tool.get("settings", {}).items()}
                    tool["parameters"] = parameters
                    logger.debug(f"\ntool parameters: {parameters}\nsettings: {tool['settings']}")

            if not for_log:
                if parameter.type == "array[tools]":
                    value = cast(list[dict[str, Any]], value)
                    tool_value = []
                    for tool in value:
                        provider_type = ToolProviderType(tool.get("type", ToolProviderType.BUILT_IN.value))
                        setting_params = tool.get("settings", {})
                        parameters = tool.get("parameters", {})
                        manual_input_params = [key for key, value in parameters.items() if value is not None]

                        parameters = {**parameters, **setting_params}

                        extra = tool.get("extra", {})

                        tool_runtime = ToolManager.get_workflow_tool_runtime(
                            tenant_id=self.tenant_id,
                            app_id=self.app_id,
                            workflow_id=self.workflow_id,
                            node_id=self.node_id,
                            workflow_tool=ToolEntity(
                                provider_id=tool.get("provider_name", ""),
                                provider_type=provider_type.value,
                                provider_name=tool.get("provider_name", ""),
                                tool_name=tool.get("tool_name", ""),
                                tool_label=tool.get("tool_label", ""),
                                tool_configurations=parameters,
                            ),
                            invoke_from=self.invoke_from
                        )
                        for parameter in tool_runtime.parameters:
                            if (parameter.type in {
                                    ToolParameter.ToolParameterType.SYSTEM_FILES,
                                    ToolParameter.ToolParameterType.FILE,
                                    ToolParameter.ToolParameterType.FILES,
                                }
                                and parameter.required
                            ):
                                raise ValueError(f"file type parameter {parameter.name} not supported in agent")
                        logger.info("Tool runtime: %s", tool_runtime)
                        if tool_runtime.description:
                            tool_runtime.description.llm = (
                                extra.get("descrption", "") or tool_runtime.description.llm
                            )
                        for tool_runtime_params in tool_runtime.parameters:
                            tool_runtime_params.form = (
                                ToolParameter.ToolParameterForm.FORM
                                if tool_runtime_params.name in manual_input_params
                                else tool_runtime_params.form
                            )
                        manual_input_value = {}
                        if tool_runtime.parameters:
                            manual_input_value = {
                                key: value for key, value in parameters.items() if key in manual_input_params
                            }
                        runtime_parameters = {
                            **tool_runtime.runtime.runtime_parameters,
                            **manual_input_value,
                        }
                        tool_value.append(
                            {
                                **tool_runtime.model_dump(mode="json", exclude={"workflow_entities"}),
                                "runtime_parameters": runtime_parameters,
                                "provider_type": provider_type.value,
                                "provider_id": tool_runtime.identity.provider,
                                "tool_name": tool_runtime.identity.name,
                            }
                        )
                    value = tool_value
                if parameter.type == "model-selector":
                    value = cast(dict[str, Any], value)
                    model_instance, model_schema = self._fetch_model(value)
                    # memory config
                    history_prompt_messages = []
                    if node_data.memory:
                        memory = self._fetch_memory(model_instance)
                        if memory:
                            prompt_messages = memory.get_history_prompt_messages(
                                message_limit=node_data.memory.window.size if node_data.memory.window.size else None
                            )
                            history_prompt_messages = [
                                prompt_message.model_dump(mode="json") for prompt_message in prompt_messages
                            ]
                    value["history_prompt_messages"] = history_prompt_messages
                    if model_schema:
                        # remove structured output feature to support old version agent plugin
                        model_schema = self._remove_unsupported_model_features_for_old_version(model_schema)
                        value["entity"] = model_schema
                        value["model"] = model_schema.model
                    else:
                        value["entity"] = None
            result[parameter_name] = value

        return result

    @classmethod
    def _extract_variable_selector_to_variable_mapping(
        cls,
        *,
        graph_config: Mapping[str, Any],
        node_id: str,
        node_data: BaseNodeData,
    ) -> Mapping[str, Sequence[str]]:
        """
        Extract variable selector to variable mapping
        :param graph_config: graph config
        :param node_id: node id
        :param node_data: node data
        :return:
        """
        node_data = cast(AgentNodeData, node_data)
        result: dict[str, Any] = {}
        for parameter_name in node_data.agent_parameters:
            input = node_data.agent_parameters[parameter_name]
            if input.type in ["mixed", "constant"]:
                selectors = VariableTemplateParser(str(input.value)).extract_variable_selectors()
                for selector in selectors:
                    result[selector.variable] = selector.value_selector
            elif input.type == "variable":
                result[parameter_name] = input.value

        result = {node_id + "." + key: value for key, value in result.items()}

        return result

    @property
    def agent_strategy_icon(self) -> str | None:
        """
        Get agent strategy icon
        :return:
        """
        provider_name = cast(AgentNodeData, self.node_data).agent_strategy_provider_name
        provider = BuiltinAgentStrategyManager.get_builtin_provider(provider_name)
        if provider:
            return f"{dify_config.CONSOLE_API_URL}/console/api/workspaces/current/agent-provider/{provider_name}/icon"

        return None

    def _fetch_memory(self, model_instance: ModelInstance) -> Optional[TokenBufferMemory]:
        # get conversation id
        conversation_id_variable = self.graph_runtime_state.variable_pool.get(
            ["sys", SystemVariableKey.CONVERSATION_ID.value]
        )
        if not isinstance(conversation_id_variable, StringSegment):
            return None
        conversation_id = conversation_id_variable.value

        # get conversation
        conversation = (
            db.session.query(Conversation)
            .filter(Conversation.app_id == self.app_id, Conversation.id == conversation_id)
            .first()
        )

        if not conversation:
            return None

        memory = TokenBufferMemory(conversation=conversation, model_instance=model_instance)

        return memory

    def _fetch_model(self, value: dict[str, Any]) -> tuple[ModelInstance, AIModelEntity | None]:
        provider_manager = ProviderManager()
        provider_model_bundle = provider_manager.get_provider_model_bundle(
            tenant_id=self.tenant_id, provider=value.get("provider", ""), model_type=ModelType.LLM
        )
        model_name = value.get("modelId", "")
        model_credentials = provider_model_bundle.configuration.get_current_credentials(
            model_type=ModelType.LLM, model=model_name
        )
        provider_name = provider_model_bundle.configuration.provider.provider
        model_type_instance = provider_model_bundle.model_type_instance
        model_instance = ModelManager().get_model_instance(
            tenant_id=self.tenant_id,
            provider=provider_name,
            model_type=ModelType.LLM,
            model=model_name,
        )
        model_schema = model_type_instance.get_model_schema(model_name, model_credentials)
        return model_instance, model_schema

    def _remove_unsupported_model_features_for_old_version(self, model_schema: AIModelEntity) -> AIModelEntity:
        if model_schema.features:
            for feature in model_schema.features:
                if feature.value not in AgentOldVersionModelFeatures:
                    model_schema.features.remove(feature)
        return model_schema

    def _transform_message(
        self,
        messages: Generator[AgentInvokeMessage, None, None],
        tool_info: Mapping[str, Any],
        parameters_for_log: dict[str, Any],
    ) -> Generator:
        """
        Convert ToolInvokeMessages into tuple[plain_text, files]
        """
        # transform message and handle file storage
        message_stream = ToolFileMessageTransformer.transform_tool_invoke_messages(
            messages=list(messages),
            user_id=self.user_id,
            tenant_id=self.tenant_id,
            conversation_id=None,
        )

        text = ""
        files: list[File] = []
        json: list[dict] = []

        agent_logs: list[AgentLogEvent] = []
        agent_execution_metadata: Mapping[NodeRunMetadataKey, Any] = {}

        variables: dict[str, Any] = {}

        for message in message_stream:
            if message.type in {
                AgentInvokeMessage.MessageType.IMAGE_LINK,
                AgentInvokeMessage.MessageType.BINARY_LINK,
                AgentInvokeMessage.MessageType.IMAGE,
            }:
                assert isinstance(message.message, AgentInvokeMessage.TextMessage)

                url = message.message.text
                if message.meta:
                    transfer_method = message.meta.get("transfer_method", FileTransferMethod.TOOL_FILE)
                else:
                    transfer_method = FileTransferMethod.TOOL_FILE

                tool_file_id = str(url).split("/")[-1].split(".")[0]

                with Session(db.engine) as session:
                    stmt = select(ToolFile).where(ToolFile.id == tool_file_id)
                    tool_file = session.scalar(stmt)
                    if tool_file is None:
                        raise ToolFileError(f"Tool file {tool_file_id} does not exist")

                mapping = {
                    "tool_file_id": tool_file_id,
                    "type": file_factory.get_file_type_by_mime_type(tool_file.mimetype),
                    "transfer_method": transfer_method,
                    "url": url,
                }
                file = file_factory.build_from_mapping(
                    mapping=mapping,
                    tenant_id=self.tenant_id,
                )
                files.append(file)
            elif message.type == AgentInvokeMessage.MessageType.BLOB:
                # get tool file id
                assert isinstance(message.message, AgentInvokeMessage.TextMessage)
                assert message.meta

                tool_file_id = message.message.text.split("/")[-1].split(".")[0]
                with Session(db.engine) as session:
                    stmt = select(ToolFile).where(ToolFile.id == tool_file_id)
                    tool_file = session.scalar(stmt)
                    if tool_file is None:
                        raise ToolFileError(f"tool file {tool_file_id} not exists")

                mapping = {
                    "tool_file_id": tool_file_id,
                    "transfer_method": FileTransferMethod.TOOL_FILE,
                }

                files.append(
                    file_factory.build_from_mapping(
                        mapping=mapping,
                        tenant_id=self.tenant_id,
                    )
                )
            elif message.type == AgentInvokeMessage.MessageType.TEXT:
                assert isinstance(message.message, AgentInvokeMessage.TextMessage)
                text += message.message.text
                yield RunStreamChunkEvent(
                    chunk_content=message.message.text, from_variable_selector=[self.node_id, "text"]
                )
            elif message.type == AgentInvokeMessage.MessageType.JSON:
                assert isinstance(message.message, AgentInvokeMessage.JsonMessage)
                if self.node_type == NodeType.AGENT:
                    msg_metadata = message.message.json_object.pop("execution_metadata", {})
                    agent_execution_metadata = {
                        key: value
                        for key, value in msg_metadata.items()
                        if key in NodeRunMetadataKey.__members__.values()
                    }
                json.append(message.message.json_object)
            elif message.type == AgentInvokeMessage.MessageType.LINK:
                assert isinstance(message.message, AgentInvokeMessage.TextMessage)
                stream_text = f"Link: {message.message.text}\n"
                text += stream_text
                yield RunStreamChunkEvent(chunk_content=stream_text, from_variable_selector=[self.node_id, "text"])
            elif message.type == AgentInvokeMessage.MessageType.VARIABLE:
                assert isinstance(message.message, AgentInvokeMessage.VariableMessage)
                variable_name = message.message.variable_name
                variable_value = message.message.variable_value
                if message.message.stream:
                    if not isinstance(variable_value, str):
                        raise ValueError("When 'stream' is True, 'variable_value' must be a string.")
                    if variable_name not in variables:
                        variables[variable_name] = ""
                    variables[variable_name] += variable_value

                    yield RunStreamChunkEvent(
                        chunk_content=variable_value, from_variable_selector=[self.node_id, variable_name]
                    )
                else:
                    variables[variable_name] = variable_value
            elif message.type == AgentInvokeMessage.MessageType.FILE:
                assert message.meta is not None
                files.append(message.meta["file"])
            elif message.type == AgentInvokeMessage.MessageType.LOG:
                assert isinstance(message.message, AgentInvokeMessage.LogMessage)
                if message.message.metadata:
                    icon = tool_info.get("icon", "")
                    dict_metadata = dict(message.message.metadata)
                    if dict_metadata.get("provider"):
                        try:
                            builtin_tool = next(
                                provider
                                for provider in BuiltinToolManageService.list_builtin_tools(
                                    self.user_id,
                                    self.tenant_id,
                                )
                                if provider.name == dict_metadata["provider"]
                            )
                            icon = builtin_tool.icon
                        except StopIteration:
                            pass

                        dict_metadata["icon"] = icon
                        message.message.metadata = dict_metadata
                agent_log = AgentLogEvent(
                    id=message.message.id,
                    node_execution_id=self.id,
                    parent_id=message.message.parent_id,
                    error=message.message.error,
                    status=message.message.status.value,
                    data=message.message.data,
                    label=message.message.label,
                    metadata=message.message.metadata,
                    node_id=self.node_id,
                )

                # check if the agent log is already in the list
                for log in agent_logs:
                    if log.id == agent_log.id:
                        # update the log
                        log.data = agent_log.data
                        log.status = agent_log.status
                        log.error = agent_log.error
                        log.label = agent_log.label
                        log.metadata = agent_log.metadata
                        break
                else:
                    agent_logs.append(agent_log)

                yield agent_log

        yield RunCompletedEvent(
            run_result=NodeRunResult(
                status=WorkflowNodeExecutionStatus.SUCCEEDED,
                outputs={"text": text, "files": files, "json": json, **variables},
                metadata={
                    **agent_execution_metadata,
                    NodeRunMetadataKey.TOOL_INFO: tool_info,
                    NodeRunMetadataKey.AGENT_LOG: agent_logs,
                },
                inputs=parameters_for_log,
            )
        )
