import base64
import hashlib
import json
import logging
import re
import uuid
from concurrent.futures import <PERSON>hr<PERSON><PERSON>oolExecutor
from datetime import datetime, timezone
from typing import Optional, Union, Dict, List, Any
from urllib.parse import quote
from dataclasses import dataclass

import lark_oapi
import requests
import tempfile
import os
from flask import Flask
from lark_oapi import JSO<PERSON>, EventContext, UTF_8, Strings, AESCipher
from lark_oapi.api.im.v1 import PatchMessageResponse, CreateMessageResponse, GetMessageResourceRequest, GetMessageResourceResponse
from lark_oapi.core.exception import AccessDeniedException, NoAuthorizationException

from core.app.entities.app_invoke_entities import InvokeFrom
from core.app.features.rate_limiting.rate_limit import RateLimitGenerator
from core.utils.feishu_auth_service import FeishuAuthorizationService
from extensions.ext_database import db
from extensions.ext_redis import redis_client
from libs import rsa
from models import Account, <PERSON><PERSON>, Tenant, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ser, MessageFeedback, Message
from models.extensions import FeishuApp, FeishuImMessage
from services.account_service import AccountService
from services.app_generate_service import AppGenerateService
from secrets import compare_digest

from services.feishu.feishu_message_send import FeishuMessageSend, SendMessageAction
from services.feishu.it_coa_service import ITCoaService
from services.file_service import FileService

# 常量
APP_BIND_FEISHU_APP_KEY_PREFIX = "app_bind_feishu_app"
IMAGE_PLACEHOLDER: str = "__image_loading__"


@dataclass
class FeishuMessageContent:
    """飞书消息内容结构体"""
    texts: List[str]
    images: List[str]  # image_key 列表
    files: List[Dict[str, str]]  # [{"file_key": "xxx", "file_name": "xxx"}]
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "texts": self.texts,
            "images": self.images,
            "files": self.files
        }


class FeishuService:
    app: Flask
    pool: ThreadPoolExecutor

    def init_app(self, app: Flask):
        self.app = app
        self.pool = ThreadPoolExecutor(
            max_workers=app.config.get("FEISHU_THREAD_MAX_WORKERS"),
            thread_name_prefix="feishu-service"
        )

    def handle_webhook(self, body: bytes, timestamp, nonce, signature, app_id: uuid.UUID) -> dict:
        data = json.loads(body)
        event_id = ""
        if "header" in data:
            event_id = data['header']['event_id']
        logging.info(
            f"event_id:{event_id} Received feishu webhook, app_id: {app_id} , timestamp: {timestamp}, nonce: {nonce}, "
            f"signature: {signature}")

        feishu_app = self.get_explain_feishu_app(app_id)
        if not feishu_app:
            logging.warning(f"event_id:{event_id} Feishu app not found, app_id: {app_id}")
            raise Exception(f"Feishu app not found for app_id: {app_id}")

        logging.info(f"event_id:{event_id} Processing feishu webhook, app_id: {app_id}")

        if "encrypt" in data:
            logging.info(f" event_id:{event_id} Decrypting data")
            plaintext = self._decrypt(feishu_app.encrypt_key, body, event_id)
            logging.debug(f"event_id:{event_id} Decrypted plaintext: {plaintext}")
            msg = JSON.unmarshal(plaintext, EventContext)
            logging.debug(f"event_id:{event_id} Processing feishu event, app_id: {app_id}, msg: {plaintext}")

        else:
            logging.debug(f"event_id:{event_id} Processing feishu event, app_id: {app_id}, msg: {str(body)}")

            msg = JSON.unmarshal(body, lark_oapi.EventContext)

        if msg.challenge:
            return {"challenge": msg.challenge}

        if feishu_app.encrypt_key:
            self.verify_signature(timestamp, nonce, feishu_app.encrypt_key, body, signature, event_id)

        if feishu_app.verification_token and feishu_app.verification_token != "":
            logging.info(f"event_id:{event_id} Verifying verification token")
            if not compare_digest(feishu_app.verification_token, msg.header.token):
                logging.warning("event_id:{event_id} verification token verification failed")
                raise AccessDeniedException("verification token verification failed")

        app = feishu_app.app

        ## 处理 feedback
        if msg.header.event_type == "card.action.trigger":
            client = self._create_feishu_client(feishu_app)
            self._handel_feedback(app, feishu_app, client, msg)
            return {
                "toast": {
                    "type": "success",
                    "content": "✨ 反馈提交成功！我们会根据您的建议持续改进。"
                }
            }

        # if "challenge" in data:
        #     return {"challenge": data["challenge"]}
        # self.pool.submit(self._process_webhook, body, timestamp, nonce, signature, app_id)
        self.pool.submit(self._process_webhook, app=app, feishu_app=feishu_app, msg=msg)

        return {"status": "received"}

    def handle_callback(self, callback_id: str) -> Dict[str, str]:
        try:
            # 输入验证
            if not callback_id or not isinstance(callback_id, str) or not callback_id.strip():
                logging.warning(f"Invalid or missing callback_id: {callback_id}")
                return {"status": "error", "message": "Invalid or missing callback_id"}

            # 从 Redis 获取回调数据
            value = redis_client.get(f"feishu:auth:callback:{callback_id}")
            if not value:
                logging.warning(f"Callback ID not found: {callback_id}")
                return {"status": "error", "message": "Callback ID not found"}

            # 解析 Redis 数据
            try:
                app_id, user_name, message_id = value.decode('utf-8').split(':')
            except ValueError as ve:
                logging.error(f"Error parsing Redis value for callback_id: {callback_id}. Error: {ve}")
                return {"status": "error", "message": "Invalid Redis data format"}

            # 获取 Feishu 应用
            feishu_app = self.get_explain_feishu_app(app_id)
            if not feishu_app:
                logging.warning(f"Feishu app not found for app_id: {app_id}")
                return {"status": "error", "message": "Feishu app not found"}

            logging.info(f"Processing feishu callback, app_id: {app_id}, message_id: {message_id}")

            # 异步处理回调
            app = feishu_app.app
            self.pool.submit(self._process_callback, app=app, feishu_app=feishu_app, message_id=message_id)

            return {"status": "success", "message": "Feishu authorization successful"}


        except Exception as e:
            logging.error(f"Unexpected error handling callback for callback_id: {callback_id}. Error: {e}")
            return {"status": "error", "message": "Failed to handle callback"}

    def _process_callback(self, app: App, feishu_app: FeishuApp, message_id: str):
        try:
            client = lark_oapi.Client.builder().app_id(feishu_app.feishu_app_id).app_secret(
                feishu_app.feishu_app_secret).build()

            pathResponse: PatchMessageResponse = FeishuMessageSend.send_feishu_message(client, message_id,
                                                                                       self.generate_feishu_auth_message(),
                                                                                       SendMessageAction.PATCH,
                                                                                       event_id="")
            if not pathResponse or not pathResponse.success():
                logging.error(
                    f"{SendMessageAction.REPLY} message error log_id: {pathResponse.get_log_id()} ,"
                    f" error:{pathResponse.get_troubleshooter()}")
                return

        except Exception as e:
            logging.error(f"handle_feishu_event error: {e}")

    def _process_webhook(self, app: App, feishu_app: FeishuApp, msg: EventContext):
        with self.app.app_context():
            self.handle_feishu_event(app, feishu_app=feishu_app, context=msg)

    def has_proccess_message(self, context: EventContext, app_name: str) -> bool:
        """
        是否有处理消息
        @param context: 事件上下文
        @param app_name: @名称
        """
        logging.debug(f"Checking if message should be processed, app_name: {app_name}, context: {context}")
        if hasattr(context, "message") and context.event["message"].get('chat_type') == "group":
            # 群聊消息，必须有@应用名称才处理
            mentions = context.event["message"].get("mentions", [])
            return any(mention.get("name") == app_name for mention in mentions)
        return True

    def save_feishu_im_message(self, ec: EventContext) -> Optional[str]:
        """
        添加飞书消息
        """
        try:
            feishu_im_message = FeishuImMessage()
            feishu_im_message.message = self.event_context_to_dict(ec)
            # feishu_im_message.call_app_inputs = ec.call_app_inputs
            feishu_im_message.created_at = datetime.now(timezone.utc).replace(tzinfo=None)
            feishu_im_message.updated_at = datetime.now(timezone.utc).replace(tzinfo=None)
            db.session.add(feishu_im_message)
            db.session.commit()
            return feishu_im_message.id
        except Exception as e:
            logging.error(f"Failed to add feishu im message, error: {e}")

        return None

    def get_feishu_im_message_by_id(self, id: str) -> Optional[FeishuImMessage]:

        return db.session.query(FeishuImMessage).get(id)

    def update_feishu_im_message(self, id: str, reply_message_id: str) -> None:
        """
        更新飞书消息
        """
        logging.debug(f"Update feishu im message, id: {id}, reply_message_id: {reply_message_id}")

        if not id or not reply_message_id:
            return
        feishu_im_message = db.session.query(FeishuImMessage).filter(FeishuImMessage.id == id).first()
        if feishu_im_message:
            feishu_im_message.reply_message_id = reply_message_id
            feishu_im_message.updated_at = datetime.now(timezone.utc).replace(tzinfo=None)
            feishu_im_message.reply_at = datetime.now(timezone.utc).replace(tzinfo=None)
            db.session.commit()

    def bind_feishu_app(self, app: App, args: dict, account: Account) -> FeishuApp:
        """
        绑定飞书应用
        """

        feishuApp = self.get_bind_feishu_by_app_id(app.id)

        if feishuApp:
            logging.info(f"Feishu app already bound, app_id: {app.id}")
            feishuApp.updated_at = datetime.now(timezone.utc).replace(tzinfo=None)
            redis_client.delete(f"{APP_BIND_FEISHU_APP_KEY_PREFIX}:{app.id}")
        else:
            feishuApp = FeishuApp()
            feishuApp.created_by = account.id
            feishuApp.app_id = app.id
            logging.info(f"Binding new Feishu app, app_id: {app.id}")

        feishuApp.feishu_app_name = args.get("feishu_app_name")
        feishuApp.feishu_app_id = args.get("feishu_app_id")

        feishu_app_secret = args.get("feishu_app_secret")
        if feishu_app_secret and self.decrypt_text(feishuApp.feishu_app_secret, app.tenant) != feishu_app_secret:
            feishuApp.feishu_app_secret = self.encrypt_text(feishu_app_secret, app.tenant)

        encrypt_key = args.get("encrypt_key")
        if encrypt_key and self.decrypt_text(feishuApp.encrypt_key, app.tenant) != encrypt_key:
            feishuApp.encrypt_key = self.encrypt_text(encrypt_key, app.tenant)

        verification_token = args.get("verification_token")
        if verification_token and self.decrypt_text(feishuApp.verification_token, app.tenant) != verification_token:
            feishuApp.verification_token = self.encrypt_text(verification_token, app.tenant)

        feishuApp.enable_prologue = args.get("enable_prologue", False)
        feishuApp.enable_reference = args.get("enable_reference", False)

        feishuApp.updated_by = account.id
        db.session.add(feishuApp)
        db.session.commit()

        return feishuApp

    def list_feishu_apps(self) -> list[FeishuApp]:
        """
        获取飞书应用列表
        """
        feishu_apps = db.session.query(FeishuApp).all()
        return feishu_apps

    def encrypt_text(self, text: Optional[str], tenant: Tenant) -> Optional[str]:
        """
        加密文本
        """
        if not text:
            return None
        encrypted_text = rsa.encrypt(text, tenant.encrypt_public_key)
        return base64.b64encode(encrypted_text).decode()

    def decrypt_text(self, text: Optional[str], tenant: Tenant) -> Optional[str]:
        """
        解密文本
        """
        if text:
            return rsa.decrypt(base64.b64decode(text), tenant.id)
        return None

    def get_bind_feishu_by_app_id(self, app_id) -> Optional[FeishuApp]:
        """
        获取绑定的飞书应用
        """
        feishu_app = db.session.query(FeishuApp).filter(FeishuApp.app_id == app_id).first()

        return feishu_app

    def get_bind_feishu_app(self, app: App) -> Optional[FeishuApp]:
        """
        获取绑定的飞书应用
        """
        feishu_app = db.session.query(FeishuApp).filter(FeishuApp.app_id == app.id).first()

        return feishu_app

    def event_context_to_dict(self, ec: EventContext) -> dict:
        obj = {
            "challenge": ec.challenge,
            "ts": ec.ts,
            "uuid": ec.uuid,
            "token": ec.token,
            "type": ec.type,
            "schema": ec.schema,
            "header": ec.header.__dict__ if ec.header else None,
            "event": ec.event
        }

        ## 替换 header 中 token ****

        if obj.get("header"):
            obj["header"]["token"] = "****"

        return obj

    def get_explain_feishu_app(self, app_id: uuid.UUID) -> Optional[FeishuApp]:
        """
        获取非加密飞书应用，
        """
        feishu_app: FeishuApp = self.get_feishu_app_for_cache(app_id)
        if feishu_app:
            return feishu_app
        feishu_app = self.get_bind_feishu_by_app_id(app_id)
        app = feishu_app.app
        if feishu_app:
            feishu_app.feishu_app_secret = self.decrypt_text(feishu_app.feishu_app_secret, app.tenant)
            feishu_app.encrypt_key = self.decrypt_text(feishu_app.encrypt_key, app.tenant)
            feishu_app.verification_token = self.decrypt_text(feishu_app.verification_token, app.tenant)
        # TypeError: Object of type FeishuApp is not JSON serializable

        try:
            value = JSON.marshal(feishu_app.to_dict())
            redis_client.set(name=f"{APP_BIND_FEISHU_APP_KEY_PREFIX}:{app_id}", value=value,
                             ex=86400)
        except Exception as e:
            logging.warning(f"Failed to cache feishu app, app_id: {app_id}, error: {e}")

        return feishu_app

    def handle_feishu_event(self, app: App, feishu_app: FeishuApp, context: EventContext):
        """
        处理飞书事件
        """
        event_id = context.header.event_id

        logging.debug(f"event_id:{event_id} Handling feishu event, app_id: {app.id}, event_type: {context.type}")

        # 预处理检查
        if not self._validate_event_context(context, feishu_app, event_id):
            return None

        logging.debug(f"event_id:{event_id} Event context validated, schema: {context.schema}, type: {context.type}")

        im_message_id = self.save_feishu_im_message(context)
        self._parse_event_context(context)

        client = self._create_feishu_client(feishu_app)

        # 处理特殊事件类型
        if self._handle_special_events(app, feishu_app, client, context, event_id):
            return None

        try:
            # 提取消息信息
            message_info = self._extract_message_info(context, event_id)
            if not message_info:
                return None

            # 检查用户授权
            if self._handle_user_authorization(app, client, message_info, event_id):
                return None

            # 处理消息回复
            im_reply_message_id = self._process_message_reply(app, feishu_app, client, message_info, event_id,
                                                              im_message_id)
            # 更新消息到数据库
            self.update_feishu_im_message(im_message_id, im_reply_message_id)
            return None

        except Exception as e:
            logging.exception(f"event_id:{event_id} handle_feishu_event error: {e}")
            return None

    def _validate_event_context(self, context: EventContext, feishu_app: FeishuApp, event_id: str) -> bool:
        """验证事件上下文"""
        logging.debug(
            f"event_id:{event_id} Validating event context, schema: {context.schema}, type: {context.header.event_type}")
        if not self.has_proccess_message(context, feishu_app.feishu_app_name):
            logging.info(f"event_id:{event_id}, Feishu Message not processed, app_name: {feishu_app.feishu_app_name}")
            return False
        return True

    def _parse_event_context(self, context: EventContext):
        """解析事件上下文"""
        if Strings.is_not_empty(context.schema):
            # 解析 v2 事件
            context.schema = "p2"
            context.type = context.header.event_type
            context.token = context.header.token
        elif Strings.is_not_empty(context.uuid):
            # 解析 v1 事件
            context.schema = "p1"
            context.type = context.event.get("type")

    def _create_feishu_client(self, feishu_app: FeishuApp):
        """创建飞书客户端"""
        return lark_oapi.Client.builder().app_id(feishu_app.feishu_app_id).app_secret(
            feishu_app.feishu_app_secret).build()

    def _handle_special_events(self, app: App, feishu_app: FeishuApp, client, context: EventContext,
                               event_id: str) -> bool:
        """处理特殊事件类型"""
        event_type = context.type

        logging.info(f"event_id:{event_id} Processing event type: {event_type}")

        if event_type == "im.chat.member.bot.added_v1":
            self.bot_add_group_handler(app, feishu_app, client, context)
            return True

        if event_type != "im.message.receive_v1":
            logging.warning(f"event_id:{event_id} Unsupported event type: {event_type}")
            return True

        if not feishu_app:
            logging.warning(f"event_id:{event_id} Feishu app not found, app_id: {str(app.id)}")
            return True

        return False

    def _extract_message_info(self, context: EventContext, event_id: str) -> Optional[dict]:
        """提取消息信息"""
        try:
            # 解析消息内容为结构体
            message_content = self.process_feishu_message(context.event["message"]["content"])
            message_id = context.event['message']['message_id']
            sender_id = context.event["sender"]["sender_id"]["user_id"]

            user_name = ITCoaService().get_user_info_by_feishu_user_id(sender_id)
            if not user_name:
                logging.warning(f"event_id:{event_id} Failed to get username by feishu user id: {sender_id}")
                user_name = sender_id

            logging.info(f"event_id:{event_id} message_content: {message_content.to_dict()}")

            return {
                'content': message_content,  # 现在是结构体而不是字符串
                'message_id': message_id,
                'user_name': user_name
            }
        except Exception as e:
            logging.error(f"event_id:{event_id} Failed to extract message info: {e}")
            return None

    def _handle_user_authorization(self, app: App, client, message_info: dict, event_id: str) -> bool:
        """处理用户授权检查"""
        callback_id = str(uuid.uuid4())
        auth_result = FeishuAuthorizationService().verify_authorization_for_feishu_robot(
            app, message_info['user_name'], callback_id)

        if not auth_result:
            return False

        # 发送授权消息
        auth_url = auth_result.get("auth_url", "")
        reply_response = FeishuMessageSend.send_feishu_message(
            client, message_info['message_id'],
            self.generate_feishu_auth_message(auth_url),
            SendMessageAction.REPLY, event_id=event_id)

        if not reply_response or not reply_response.success():
            self._log_feishu_error(event_id, SendMessageAction.REPLY, reply_response)
            return True

        # 保存授权回调信息
        redis_client.setex(f"feishu:auth:callback:{callback_id}", 600,
                           f"{app.id}:{message_info['user_name']}:{reply_response.data.message_id}")
        return True

    def _log_feishu_error(self, event_id: str, action: SendMessageAction, response):
        """统一的飞书错误日志记录"""
        log_id = response.get_log_id() if response and hasattr(response, 'get_log_id') else "N/A"
        error_msg = response.get_troubleshooter() if response and hasattr(response, 'get_troubleshooter') else "N/A"
        logging.error(f"event_id:{event_id} {action} message error log_id: {log_id}, error: {error_msg}")

    def _process_message_reply(self, app: App, feishu_app: FeishuApp, client, message_info: dict, event_id: str,
                               im_message_id: str) -> str:
        """处理消息回复"""

        if FeishuMessageSend.has_create_card_permission(client=client):
            return self._handle_streaming_reply(app, client, message_info, event_id, feishu_app=feishu_app,
                                                im_message_id=im_message_id)
        else:
            return self._handle_non_streaming_reply(app, feishu_app, client, message_info, event_id, im_message_id)

    def _handle_streaming_reply(self, app: App, client, message_info: dict, event_id: str,
                                feishu_app: FeishuApp, im_message_id: str) -> str:
        """处理流式回复"""
        logging.info(f"event_id:{event_id} Streaming enabled, processing message")
        content_element_id = FeishuMessageSend.generate_element_id()

        card_id = FeishuMessageSend.create_and_send_streaming_card(
            client=client, message_id=message_info['message_id'],
            event_id=event_id, element_id=content_element_id, im_message_id=im_message_id)

        if not card_id:
            logging.error(f"event_id:{event_id} Failed to create streaming card")
            return None

        logging.debug(f"event_id:{event_id} card_id: {card_id}")

        # 将消息内容转换为 Dify API 参数
        dify_args = self._build_args_from_content(
            content=message_info['content'],
            user_name=message_info['user_name'],
            client=client,
            message_id=message_info['message_id'],
            app=app
        )
        
        try:
            response = self.call_dify_app_streaming(
                args=dify_args,
                user_name=message_info['user_name'],
                app=app)
        except Exception as e:
            logging.error(f"event_id:{event_id} Failed to call dify app streaming: {e}")
            # 发送错误消息
            error_content = f"抱歉，处理您的请求时遇到了问题：{str(e)}"
            FeishuMessageSend.send_streaming_card_update(
                client=client, event_id=event_id,
                element_id=content_element_id, card_id=card_id,
                content=error_content, sequence=1)
            return ""

        answer = ""
        im_reply_message_id = ""
        sequence = 1
        is_message_end = False
        final_metadata = None  # 保存最终的metadata

        # 异步发送控制变量
        is_sending = False
        pending_content = ""
        last_sent_content = ""
        send_future = None

        def _add_metadata_references(content: str, metadata: dict) -> str:
            """添加metadata引用信息的辅助函数"""
            if not metadata or not feishu_app.enable_reference:
                return content

            result = content + "\n\n---\n\n"
            if "retriever_resources" in metadata:
                _retriever_resources = metadata["retriever_resources"]
                retriever_resources = list({v['document_id']: v for v in _retriever_resources}.values())
                for resource in retriever_resources:
                    document_name = resource.get("document_name")
                    source = resource.get("source")
                    result += f"- [{document_name}]({source}) \n"
            return result

        def _send_final_update(content: str, metadata: dict = None):
            """发送最终更新的辅助函数"""
            nonlocal sequence
            final_content = self._process_final_images(client, content, event_id)
            if metadata:
                final_content = _add_metadata_references(final_content, metadata)

            FeishuMessageSend.send_streaming_card_update(
                client=client, event_id=event_id,
                element_id=content_element_id, card_id=card_id,
                content=final_content, sequence=sequence)
            sequence += 1

        # 处理流式响应
        for chunk in response:
            if chunk is None:
                continue

            if isinstance(chunk, str) and chunk.startswith('data:'):
                json_data = chunk[len('data:'):].strip()
                try:
                    data = json.loads(json_data)
                except json.JSONDecodeError as e:
                    logging.warning(f"Failed to decode JSON data: {e}")
                    continue

                # 处理消息结束事件
                if data.get("event") == "message_end":
                    is_message_end = True
                    if "metadata" in data:
                        final_metadata = data["metadata"]
                        logging.debug(f"event_id:{event_id} received metadata {json.dumps(data)}")

                    # 等待当前发送任务完成
                    if send_future:
                        send_future.result()

                    # 发送最终内容
                    _send_final_update(answer, final_metadata)
                    break

                # 处理答案内容
                if "answer" in data and data["answer"]:
                    answer += data["answer"]
                    streaming_content = self._process_images_for_streaming(answer)

                    # 检查并清理已完成的发送任务
                    if send_future and send_future.done():
                        is_sending = False
                        send_future = None

                    # 异步发送逻辑
                    if not is_sending:
                        # 没有正在进行的发送任务，立即发送
                        is_sending = True
                        last_sent_content = streaming_content
                        send_future = self.pool.submit(
                            FeishuMessageSend.send_streaming_card_update,
                            client=client, event_id=event_id,
                            element_id=content_element_id, card_id=card_id,
                            content=streaming_content, sequence=sequence)
                        sequence += 1
                    else:
                        # 有正在进行的发送任务，累积内容
                        pending_content = streaming_content

                # 保存消息ID
                if "message_id" in data:
                    im_reply_message_id = data["message_id"]

        return im_reply_message_id

    def _process_images_for_streaming(self, content: str) -> str:
        """流式处理过程中的图片处理：将markdown图片转换为带特殊标记的普通链接 ，主要解决飞书图片闪屏问题"""
        import re

        # 正则表达式匹配markdown图片格式: ![alt](url)
        image_pattern = r'!\[([^\]]*)\]\(([^\)]+)\)'

        def convert_to_link(match):
            alt_text = match.group(1)
            image_url = match.group(2)

            # 检查是否已经是飞书的image_key格式（通常以img_开头）
            if image_url.startswith('img_'):
                # 如果已经是image_key，保持图片格式不变
                return match.group(0)

            # 将图片转换为带特殊标记的普通链接格式
            # 使用特殊前缀 IMAGE_PLACEHOLDER  来标记这是临时转换的图片链接 ;
            link_text = alt_text if alt_text else "图片链接"
            return f'[{IMAGE_PLACEHOLDER}{link_text}]({image_url})'

        # 替换所有图片为带标记的普通链接
        processed_content = re.sub(image_pattern, convert_to_link, content)
        return processed_content

    def _handle_non_streaming_reply(self, app: App, feishu_app: FeishuApp, client, message_info: dict,
                                    event_id: str, im_message_id: str) -> str:
        """处理非流式回复"""
        initial_content = self.app.config.get("FEISHU_REPLY_MESSAGE")
        logging.info(f"event_id:{event_id} reply initial_content message")

        reply_response = FeishuMessageSend.send_feishu_message(
            client, message_info['message_id'],
            self.generate_feishu_message(initial_content, None, enable_feedback=False, im_message_id="",
                                         feedback_content="", rating=""),
            SendMessageAction.REPLY, event_id=event_id)

        if not reply_response or not reply_response.success():
            self._log_feishu_error(event_id, SendMessageAction.REPLY, reply_response)
            return ""

        # 将消息内容转换为 Dify API 参数
        dify_args = self._build_args_from_content(
            content=message_info['content'],
            user_name=message_info['user_name'],
            client=client,
            message_id=message_info['message_id'],
            app=app
        )
        
        answer, im_reply_message_id, retriever_resources = self.call_dify_app(
            args=dify_args,
            user_name=message_info['user_name'],
            app=app,
            retriever=feishu_app.enable_reference,
            event_id=event_id)

        logging.debug(
            f"call_dify_app Response: event_id:{event_id} Dify app response: {answer}, im_reply_message_id: {im_reply_message_id}")

        processed_answer = FeishuMessageSend.process_markdown_images(client, answer, event_id)
        logging.debug(f"processed_answer: event_id:{event_id} Processed answer: {processed_answer}")

        message_id = reply_response.data.message_id
        patch_response = FeishuMessageSend.send_feishu_message(
            client, message_id,
            self.generate_feishu_message(processed_answer, retriever_resources, enable_feedback=True,
                                         im_message_id=message_id, feedback_content="", rating=""),
            SendMessageAction.PATCH, event_id=event_id)

        if not patch_response or not patch_response.success():
            log_id = patch_response.get_log_id() if patch_response else "N/A"
            feishu_error_msg = json.dumps(patch_response.error, ensure_ascii=False)
            logging.error(
                f"event_id:{event_id} {SendMessageAction.PATCH} message error log_id: {log_id}, error: {feishu_error_msg}")
            return ""
        return im_reply_message_id

    def _process_final_images(self, client, content: str, event_id: str) -> str:
        """最终图片处理：只将临时转换的链接重新转换为图片格式"""
        import re
        # 只匹配带有特殊标记的临时图片链接: [__TEMP_IMG__text](url)
        temp_image_link_pattern = r'\[{__image_loading__}([^\]]+)\]\(([^\)]+)\)'

        def convert_temp_link_to_image(match):
            # 去掉特殊标记前缀，恢复原始的alt文本
            original_alt_text = match.group(1)
            image_url = match.group(2)

            # 将临时链接转换回markdown图片格式
            return f'![{original_alt_text}]({image_url})'

        # 先将带特殊标记的临时链接转换为markdown图片格式
        content_with_images = re.sub(temp_image_link_pattern, convert_temp_link_to_image, content)

        # 然后使用原有的process_markdown_images方法处理所有图片
        final_content = FeishuMessageSend.process_markdown_images(client, content_with_images, event_id)

        return final_content

    def bot_add_group_handler(self, app: App, feishu: FeishuApp, client, context: EventContext):
        """
        处理飞书机器人加入群聊事件
        """
        event_id = context.header.event_id

        if feishu.enable_prologue:
            if app.mode in [AppMode.ADVANCED_CHAT.value, AppMode.WORKFLOW.value]:
                opening_statement = app.workflow.features_dict.get("opening_statement")
            else:
                opening_statement = app.app_model_config.opening_statement

            if opening_statement:
                message_id = context.event['chat_id']
                message_content=self.generate_feishu_message(opening_statement, None, enable_feedback=False,
                                             im_message_id=message_id, feedback_content="", rating="")
                response: CreateMessageResponse = FeishuMessageSend.send_feishu_message(client, message_id, message_content,
                                                                                        SendMessageAction.SEND, event_id=event_id)
                if not response or not response.success():
                    logging.error(
                        f"event_id:{event_id} {SendMessageAction.SEND} message error log_id: {response.get_log_id()} , "
                        f"error:{response.get_troubleshooter()}")
        return

    def process_feishu_message(self, message_str: str) -> FeishuMessageContent:
        """
        解析飞书消息，返回统一的内容结构
        """
        try:
            message_dict = json.loads(message_str)
            
            # 单独文本消息
            if "text" in message_dict:
                return self._process_text_message(message_dict["text"])
            
            # 单独图片消息
            elif "image_key" in message_dict:
                return self._process_image_message(message_dict["image_key"])
            
            # 单独文件消息
            elif "file_key" in message_dict:
                return self._process_file_message(
                    message_dict["file_key"],
                    message_dict.get("file_name", "")
                )
            
            # 富文本内容（文本+图片、@机器人等）
            elif "content" in message_dict:
                return self._process_rich_content(message_dict["content"])
            
            else:
                logging.warning(f"Unsupported message format: {message_dict}")
                return FeishuMessageContent(
                    texts=["不支持的消息格式"],
                    images=[],
                    files=[]
                )
                
        except Exception as e:
            logging.error(f"Failed to process feishu message, error: {e}")
            # 向后兼容：如果解析失败，将原始字符串作为文本处理
            cleaned_text = self.clean_at_mentions(message_str)
            return FeishuMessageContent(
                texts=[cleaned_text] if cleaned_text.strip() else [],
                images=[],
                files=[]
            )

    def clean_at_mentions(self, message_content: str) -> str:
        """
        过滤消息内容

        """
        message_content = message_content.replace("@_all", "")
        result = re.sub(r'@_user_\d+\s*', '', message_content)

        return result

    ## 调用 dify 应用生成返回流式结果
    def call_dify_app_streaming(self, args: Dict[str, Any], user_name: str, app: App) -> RateLimitGenerator:
        """
        调用 Dify Service 流式结果
        """
        end_user = self.create_or_update_end_user_for_user_id(app, user_name)
        streaming = True

        # 确保 response_mode 为 streaming
        args["response_mode"] = "streaming"

        if app.mode not in [AppMode.CHAT.value, AppMode.AGENT_CHAT.value, AppMode.COMPLETION.value,
                            AppMode.ADVANCED_CHAT]:
            logging.info(f"Unsupported app mode: {app.mode}")
            raise Exception(f"unsupported app mode:{app.mode}")

        try:
            response: Union[dict, RateLimitGenerator] = AppGenerateService.generate(
                app_model=app, user=end_user, args=args, invoke_from=InvokeFrom.SERVICE_API, streaming=streaming
            )
        except Exception as e:
            logging.error(f"Failed to generate response from AppGenerateService: {e}")
            raise e

        if isinstance(response, RateLimitGenerator):
            return response

        raise Exception("Unsupported response type")

    def call_dify_app(self, args: Dict[str, Any], user_name: str, app: App, retriever: bool = False, event_id: str = "") -> [str,
                                                                                                                   str,
                                                                                                                   Optional[
                                                                                                                       list[
                                                                                                                           dict]]]:

        """
        调用 Dify Service
        """
        end_user = self.create_or_update_end_user_for_user_id(app, user_name)
        streaming = True

        # 确保 response_mode 为 blocking
        args["response_mode"] = "blocking"

        message_id: str = ""
        retriever_resources: list[dict] = []

        if app.mode not in [AppMode.CHAT.value, AppMode.AGENT_CHAT.value, AppMode.COMPLETION.value,
                            AppMode.ADVANCED_CHAT]:
            logging.info(f"event_id:{event_id} Unsupported app mode: {app.mode}")
            return f"unsupported app mode:{app.mode}", "", None

        try:
            response: Union[dict, RateLimitGenerator] = AppGenerateService.generate(
                app_model=app, user=end_user, args=args, invoke_from=InvokeFrom.SERVICE_API, streaming=streaming
            )
        except Exception as e:
            logging.error(f"event_id:{event_id} Failed to generate response from AppGenerateService: {e}")
            return f"抱歉，处理您的请求时遇到了问题：{str(e)}", "", None

        if response is None:
            return "No response", "", None

        if isinstance(response, dict):
            return response.get("answer", "No answer"), response.get("message_id", ""), None

        if isinstance(response, RateLimitGenerator):
            answer = ""
            for chunk in response:
                # 如果 chunk 以 'data:' 开头，去掉这个前缀
                if isinstance(chunk, str) and chunk.startswith('data:'):
                    json_data = chunk[len('data:'):].strip()  # 去掉前缀并清理空白
                    try:
                        # 解析 JSON 数据
                        data = json.loads(json_data)
                    except json.JSONDecodeError as e:
                        logging.warning(f"Failed to decode JSON data: {e}")
                        # 如果 JSON 解析失败，继续到下一个 chunk
                        continue
                    # 检查 JSON 数据中是否有 "answer" 键
                    if "answer" in data:
                        answer += data["answer"]
                    if "message_id" in data:
                        message_id = data["message_id"]
                    if "metadata" in data:
                        logging.debug(f"event_id:{event_id} metadata {json.dumps(data)}")

                        metadata = data["metadata"]
                        if retriever and "retriever_resources" in metadata:
                            _retriever_resources = metadata["retriever_resources"]
                            # 根据document_id获取去重
                            retriever_resources = list({v['document_id']: v for v in _retriever_resources}.values())
            logging.debug(f"event_id:{event_id} retriever_resources_size {len(retriever_resources)}")

            return answer, message_id, retriever_resources

        return "unsupported response type", "", None

    def verify_signature(self, timestamp, nonce, encrypt_key, body, signature, event_id: str) -> bool:
        logging.debug(
            f"event_id:{event_id} verify_signature: timestamp={timestamp}, nonce={nonce}, encrypt_key={encrypt_key}, body={body}, signature={signature}")
        bs = (timestamp + nonce + encrypt_key).encode(UTF_8) + body
        h = hashlib.sha256(bs)
        if signature != h.hexdigest():
            logging.warning(f"event_id:{event_id} signature verification failed")
            raise AccessDeniedException("signature verification failed")
        return True

    def create_or_update_end_user_for_user_id(self, app_model: App, user_id: Optional[str] = None) -> EndUser:
        """
            Create or update session terminal based on user ID.
            """
        user_id = user_id or "FeishuBot-USER"

        end_user = db.session.query(EndUser).filter(
            EndUser.tenant_id == app_model.tenant_id,
            EndUser.app_id == app_model.id,
            EndUser.session_id == user_id,
            EndUser.type == "feishu_bot",
        ).first()

        if end_user is None:
            end_user = EndUser(
                tenant_id=app_model.tenant_id,
                app_id=app_model.id,
                type="feishu_bot",
                is_anonymous=(user_id == "FeishuBot-USER"),
                session_id=user_id,
            )
            db.session.add(end_user)
            db.session.commit()

        return end_user

    def _decrypt(self, encrypt_key, content: bytes, event_id: str) -> str:
        plaintext: str
        encrypt = json.loads(content).get("encrypt")
        if Strings.is_not_empty(encrypt):
            if Strings.is_empty(encrypt_key):
                logging.warning(f"event_id:{event_id} encrypt_key not found")
                raise NoAuthorizationException("encrypt_key not found")
            plaintext = AESCipher(encrypt_key).decrypt_str(encrypt)
        else:
            plaintext = str(content, UTF_8)

        return plaintext

    def get_feishu_app_for_cache(self, app_id: uuid.UUID) -> Optional[FeishuApp]:
        """
        获取飞书应用
        """
        try:
            feishu_app = redis_client.get(f"{APP_BIND_FEISHU_APP_KEY_PREFIX}:{app_id}")
            if feishu_app:
                feishu_app = feishu_app.decode("utf-8")
                feishu_app = FeishuApp().from_dict(JSON.unmarshal(feishu_app, dict))
                return feishu_app
        except Exception as e:
            logging.warning(f"Failed to get feishu app for cache, app_id: {app_id}, error: {e}")

        return None

    def _handel_feedback(self, app: App, feishu_app: FeishuApp, client, context: EventContext) -> None:
        """
        处理飞书反馈事件
        """
        try:
            logging.debug(f"Handling feedback")

            event_id = context.header.event_id
            value = context.event["action"]['value']
            if not value:
                logging.warning(f"event_id:{event_id} feedback value not found")
                return

            logging.info(f"event_id:{event_id} feedback value {value}")

            rating = value['feedback']
            im_message_id = value['im_message_id']
            feedback_content = context.event["action"]['form_value']['feedback_content']
            logging.debug(f"event_id:{event_id} Feedback content: {feedback_content}")
            feishu_user_id = context.event["operator"]['user_id']
            self._save_feedback(im_message_id, rating=rating, feedback_content=feedback_content, app=app,
                                feishu_user_id=feishu_user_id)

        except Exception as e:
            ##
            logging.error(f"_handle_feedback: Failed to save feedback: {e}", exc_info=True)

    def _save_feedback(self, im_message_id: str, rating: str, feedback_content: str, app: App,
                       feishu_user_id: str) -> None:

        logging.debug(f"_save_feedback im_message_id:{im_message_id} message_feedback:{feedback_content}")

        feishu_iam_message = self.get_feishu_im_message_by_id(im_message_id)
        if not feishu_iam_message:
            logging.warning(f"_save_feedback: not found feishu_iam_message by im_message_id:{im_message_id}")
            return

        message_id = feishu_iam_message.reply_message_id

        dify_message = db.session.query(Message).get(message_id)

        if not dify_message:
            logging.warning(f"_save_feedback: not found message id {message_id}")
            return

        oa_user = ITCoaService().search_user(search_type='feishu_user_id', search_text=feishu_user_id)
        if not oa_user:
            logging.warning(f"_save_feedback: not found feishu_user_id {feishu_user_id}")
            return

        account: Account = None
        email = oa_user['data'][0]['email']
        if email:
            account = AccountService.find_user_for_email(email=email)

        if not account:
            logging.warning(f"_save_feedback: not found account {feishu_user_id}")
            return

        feedback = db.session.query(MessageFeedback).filter(MessageFeedback.message_id == message_id,
                                                            MessageFeedback.from_source == "user").first()

        if feedback:
            ## 更新
            feedback.rating = rating
            feedback.content = feedback_content
        else:
            logging.info(f"_save_feedback: not found rating {rating} ,create new feedback")
            feedback = MessageFeedback(
                app_id=app.id,
                conversation_id=dify_message.conversation_id,
                message_id=dify_message.id,
                rating=rating,
                content=feedback_content,
                from_source="user",
                from_end_user_id=account.id,
                from_account_id=account.id,
            )
            db.session.add(feedback)

        db.session.commit()

        if not feedback:
            logging.warning(f"_save_feedback: save feedback failed")

    def generate_feishu_message(self, content: str, retriever_resources: Optional[list[dict]],
                                enable_feedback: bool, im_message_id: str, feedback_content: str, rating: str) -> str:
        """
            根据模版生成消息
        """

        message = {
            "schema": "2.0",
            "config": {
                "update_multi": True,
                "style": {
                    "text_size": {
                        "normal_v2": {
                            "default": "normal",
                            "pc": "normal",
                            "mobile": "heading"
                        }
                    }
                }
            },

            "body": {
                "direction": "vertical",
                "padding": "12px 12px 12px 12px",
                "elements": [
                    {
                        "tag": "markdown",
                        "content": content,
                        "text_align": "left",
                        "text_size": "normal_v2",
                        "margin": "0px 0px 0px 0px"
                    }
                ]
            }
        }
        if retriever_resources:

            message['body']["elements"].append({
                "tag": "hr",
            })
            for resource in retriever_resources:
                document_name = resource.get("document_name")
                source = resource.get("source")
                message['body']["elements"].append({
                    "tag": "markdown",
                    "content": f"- [{document_name}]({source})"
                })

        if enable_feedback:
            message['body']["elements"].append({
                "tag": "hr",
            })
            feedback_elements = FeishuMessageSend.generate_feedback_card(im_message_id, feedback_content, rating)
            message['body']["elements"].append(feedback_elements)

        return json.dumps(message)

    def generate_feishu_auth_message(self, auth_url: str = None) -> str:
        is_authorized = auth_url is None
        card_content = {
            "schema": "2.0",
            "config": {
                "update_multi": True,
                "locales": ["en_us"],
                "style": {
                    "text_size": {
                        "normal_v2": {
                            "default": "normal",
                            "pc": "normal",
                            "mobile": "heading"
                        }
                    }
                }
            },
            "body": {
                "direction": "vertical",
                "padding": "12px 12px 12px 12px",
                "elements": [
                    {
                        "tag": "markdown",
                        "content": "您访问的应用需要从飞书文档获取知识，请授权融合云平台获取您的飞书权限。",
                        "i18n_content": {
                            "en_us": "The application you are accessing requires knowledge from Feishu documents. Please authorize the LiCloud Platform to obtain your Feishu permissions."
                        },
                        "text_align": "left",
                        "text_size": "normal_v2",
                        "margin": "0px 0px 0px 0px"
                    },
                    {
                        "tag": "div",
                        "text": {
                            "tag": "plain_text",
                            "content": "",
                            "text_size": "normal_v2",
                            "text_align": "left",
                            "text_color": "default"
                        },
                        "margin": "0px 0px 0px 0px"
                    },
                    {
                        "tag": "column_set",
                        "horizontal_align": "left",
                        "columns": [
                            {
                                "tag": "column",
                                "width": "weighted",
                                "elements": [
                                    {
                                        "tag": "button",
                                        "text": {
                                            "tag": "plain_text",
                                            "content": "已飞书授权" if is_authorized else "立即授权",
                                            "i18n_content": {
                                                "en_us": "Already Authorized" if is_authorized else "🚀 Dive in"
                                            }
                                        },
                                        "type": "primary" if is_authorized else "primary_filled",
                                        "width": "fill",
                                        "size": "medium",
                                        "disabled": is_authorized,
                                        "behaviors": [] if is_authorized else [
                                            {
                                                "type": "open_url",
                                                "default_url": f"https://applink.feishu.cn/client/web_url/open?url={quote(auth_url)}&mode=sidebar-semi",
                                                "android_url": "",
                                                "ios_url": "",
                                                "pc_url": ""
                                            }
                                        ]
                                    }
                                ],
                                "direction": "horizontal",
                                "vertical_spacing": "8px",
                                "horizontal_align": "left",
                                "vertical_align": "top",
                                "weight": 1
                            }
                        ],
                        "margin": "0px 0px 0px 0px"
                    }
                ]
            },
            "header": {
                "title": {
                    "tag": "plain_text",
                    "content": "飞书授权申请",
                    "i18n_content": {
                        "en_us": "Feishu Authorization Request"
                    }
                },
                "subtitle": {
                    "tag": "plain_text",
                    "content": ""
                },
                "template": "blue",
                "icon": {
                    "tag": "standard_icon",
                    "token": "lark-logo_colorful"
                },
                "padding": "12px 12px 12px 12px"
            }
        }
        return json.dumps(card_content)

    def _process_text_message(self, text: str) -> FeishuMessageContent:
        """处理纯文本消息"""
        cleaned_text = self.clean_at_mentions(text)
        return FeishuMessageContent(
            texts=[cleaned_text] if cleaned_text.strip() else [],
            images=[],
            files=[]
        )

    def _process_image_message(self, image_key: str) -> FeishuMessageContent:
        """处理单独图片消息"""
        return FeishuMessageContent(
            texts=[],
            images=[image_key],
            files=[]
        )

    def _process_file_message(self, file_key: str, file_name: str) -> FeishuMessageContent:
        """处理单独文件消息"""
        return FeishuMessageContent(
            texts=[],
            images=[],
            files=[{"file_key": file_key, "file_name": file_name}]
        )

    def _process_rich_content(self, content: List[List[Dict]]) -> FeishuMessageContent:
        """处理富文本内容"""
        texts = []
        images = []
        files = []
        
        for paragraph in content:
            if not paragraph:  # 空段落
                continue
                
            paragraph_text = ""
            for element in paragraph:
                tag = element.get("tag")
                
                if tag == "text":
                    paragraph_text += element.get("text", "")
                # elif tag == "at":
                    # @用户提及，保留用户名但清理格式
                    # user_name = element.get("user_name", "")
                    # if user_name and not user_name.startswith("@_user_"):
                    #     paragraph_text += f"@{user_name} "
                elif tag == "img":
                    image_key = element.get("image_key", "")
                    if image_key:
                        images.append(image_key)
                elif tag == "file":
                    file_key = element.get("file_key", "")
                    file_name = element.get("file_name", "")
                    if file_key:
                        files.append({"file_key": file_key, "file_name": file_name})
            
            # 清理文本并添加到数组
            if paragraph_text.strip():
                cleaned_text = self.clean_at_mentions(paragraph_text.strip())
                if cleaned_text:
                    texts.append(cleaned_text)
        
        return FeishuMessageContent(
            texts=texts,
            images=images,
            files=files
        )

    def _build_args_from_content(self, content: FeishuMessageContent, user_name: str, client, message_id: str, app: App) -> Dict[str, Any]:
        """将消息内容转换为 Dify API 调用参数，包括下载和上传资源文件"""
        query_parts = []
        files = []
        
        # 添加文本内容
        if content.texts:
            query_parts.extend(content.texts)
        
        # 处理图片文件
        if content.images:
            for image_key in content.images:
                    try:
                        # 下载飞书图片
                        temp_file_path = self._download_feishu_resource(
                            client=client,
                            message_id=message_id,
                            file_key=image_key,
                            resource_type="image"
                        )

                        if temp_file_path:
                            # 获取文件名和MIME类型
                            file_name = os.path.basename(temp_file_path)
                            mime_type = self._get_mime_type_from_extension(file_name)

                            # 上传到 AgentOps
                            upload_file_id = self._upload_file_to_agentops(
                                file_path=temp_file_path,
                                file_name=file_name,
                                mime_type=mime_type,
                                user_name=user_name,
                                app=app
                            )

                            if upload_file_id:
                                # 添加到 files 数组中
                                files.append({
                                    "type": "image",
                                    "transfer_method": "local_file",
                                    "upload_file_id": upload_file_id
                                })
                            else:
                                logging.error(f"Failed to upload image {image_key} to AgentOps")
                        else:
                            logging.error(f"Failed to download image {image_key} from Feishu")

                    except Exception as e:
                        logging.error(f"Error processing image {image_key}: {e}")

        # 处理文件
        if content.files:
            for file_info in content.files:
                file_name = file_info.get("file_name", "未知文件")
                file_key = file_info.get("file_key", "")

                try:
                    # 根据文件类型确定处理方式
                    mime_type = self._get_mime_type_from_extension(file_name)
                    resource_type = "image" if mime_type.startswith("image/") else "file"
                    
                    # 下载飞书文件
                    temp_file_path = self._download_feishu_resource(
                        client=client,
                        message_id=message_id,
                        file_key=file_key,
                        resource_type=resource_type,
                        file_name=file_name
                    )
                    
                    if temp_file_path:
                        # 上传到 AgentOps
                        upload_file_id = self._upload_file_to_agentops(
                            file_path=temp_file_path,
                            file_name=file_name,
                            mime_type=mime_type,
                            user_name=user_name,
                            app=app
                        )
                        
                        if upload_file_id:
                            files.append({
                                "type": "image" if mime_type.startswith("image/") else "document",
                                "transfer_method": "local_file",
                                "upload_file_id": upload_file_id
                            })
                            query_parts.append(f"文件: {file_name}")
                        else:
                            logging.error(f"Failed to upload file {file_name} to AgentOps")
                    else:
                        logging.error(f"Failed to download file {file_name} from Feishu")
                        
                except Exception as e:
                    logging.error(f"Error processing file {file_name}: {e}")
        
        # 构建查询文本
        query = " ".join(query_parts) if query_parts else "  "

        # 构建完整的参数
        args = {
            "inputs": {},
            "query": query,
            "response_mode": "streaming",
            "conversation_id": "",
            "user": user_name
        }
        
        # 只有当有文件时才添加 files 字段
        if files:
            args["files"] = files
        
        # 打印请求参数日志
        logging.info(f"Built Dify API args: {json.dumps(args, ensure_ascii=False, indent=2)}")
        
        return args

    def _get_mime_type_from_extension(self, file_name: str) -> str:
        """根据文件扩展名获取MIME类型"""
        extension = file_name.lower().split('.')[-1] if '.' in file_name else ''

        mime_types = {
            'png': 'image/png',
            'jpg': 'image/jpeg',
            'jpeg': 'image/jpeg',
            'gif': 'image/gif',
            'webp': 'image/webp',
            'pdf': 'application/pdf',
            'doc': 'application/msword',
            'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'txt': 'text/plain',
            'json': 'application/json',
            'csv': 'text/csv',
            'xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        }

        return mime_types.get(extension, 'image/png')

    def _download_feishu_resource(self, client, message_id: str, file_key: str, resource_type: str = "file", file_name: str = None) -> Optional[str]:
        """
        从飞书下载资源文件到临时目录
        :param client: 飞书客户端
        :param message_id: 消息ID
        :param file_key: 文件key
        :param resource_type: 资源类型 (file/image)
        :param file_name: 文件名（可选）
        :return: 临时文件路径
        """
        try:
            # 构造请求对象
            request: GetMessageResourceRequest = GetMessageResourceRequest.builder() \
                .message_id(message_id) \
                .file_key(file_key) \
                .type(resource_type) \
                .build()

            # 发起请求
            response: GetMessageResourceResponse = client.im.v1.message_resource.get(request)

            # 处理失败返回
            if not response.success():
                logging.error(
                    f"Download feishu resource failed, code: {response.code}, msg: {response.msg}, "
                    f"log_id: {response.get_log_id()}")
                return None

            # 确定文件名
            if not file_name:
                if resource_type == "image":
                    # 对于图片类型，从 Content-Type 获取文件扩展名
                    content_type = response.raw.headers.get("Content-Type", "")
                    if content_type:
                        # 从 Content-Type 中提取扩展名，如 image/png -> png
                        extension = content_type.split('/')[-1] if '/' in content_type else "jpg"
                        file_name = f"{file_key}.{extension}"
                    else:
                        file_name = f"{file_key}.jpg"  # 默认为 jpg
                else:
                    # 对于文件类型，尝试使用响应中的文件名
                    file_name = response.file_name if hasattr(response, 'file_name') and response.file_name else f"{file_key}.{resource_type}"
            
            # 创建临时文件
            temp_dir = tempfile.gettempdir()
            temp_file_path = os.path.join(temp_dir, file_name)
            
            # 保存文件
            with open(temp_file_path, "wb") as f:
                f.write(response.file.read())
            
            logging.info(f"Successfully downloaded feishu resource: {file_key} to {temp_file_path}")
            return temp_file_path

        except Exception as e:
            logging.error(f"Failed to download feishu resource {file_key}: {e}")
            return None

    def _upload_file_to_agentops(self, file_path: str, file_name: str, mime_type: str, user_name: str, app: App) -> Optional[str]:
        """
        使用 FileService 上传文件
        :param file_path: 文件路径
        :param file_name: 文件名
        :param mime_type: MIME类型
        :param user_name: 用户名
        :param app: App 对象，用于获取 EndUser
        :return: upload_file_id
        """
        try:
            # 读取文件内容
            with open(file_path, 'rb') as f:
                content = f.read()
            
            # 获取或创建 EndUser
            end_user = self.create_or_update_end_user_for_user_id(app, user_name)
            
            # 使用 FileService 上传文件
            upload_file = FileService.upload_file(
                filename=file_name,
                content=content,
                mimetype=mime_type,
                user=end_user,
                source=None
            )
            
            logging.info(f"Successfully uploaded file using FileService: {file_name}, id: {upload_file.id}")
            return upload_file.id

        except Exception as e:
            logging.error(f"Failed to upload file using FileService: {e}")
            return None
        finally:
            # 清理临时文件
            try:
                if os.path.exists(file_path):
                    os.remove(file_path)
                    logging.debug(f"Cleaned up temporary file: {file_path}")
            except Exception as e:
                logging.warning(f"Failed to clean up temporary file {file_path}: {e}")


feishu_service = FeishuService()


def init_app(app):
    feishu_service.init_app(app)
