from flask_restful import Resource, reqparse

from controllers.console.wraps import setup_required
from controllers.inner_api import api
from controllers.inner_api.wraps import inner_api_only
from libs.helper import uuid_value
from services.account_service import AccountService, TenantService


class EnterpriseAccountInitApi(Resource):

    @setup_required
    @inner_api_only
    def post(self):
        parser = reqparse.RequestParser()
        parser.add_argument('tenant_id', type=uuid_value, required=True, location='json')
        parser.add_argument('usernames', type=list[str], required=True, location='json')
        parser.add_argument('owner', type=str, required=True, location='json')
        parser.add_argument('operator', type=str, required=True, location='json')
        args = parser.parse_args()

        tenant = TenantService.get_tenant_by_id(args['tenant_id'])
        if tenant is None:
            return {
                'message': 'tenant not found.'
            }, 404

        owner = AccountService.get_account_by_name(args['owner'])

        accounts = AccountService.bulk_add_account(user_list=args['usernames'])

        TenantService.update_tenant_members(tenant, owner, accounts)

        return {
            'message': 'enterprise account init.'
        }


api.add_resource(EnterpriseAccountInitApi, '/enterprise/workspace/account/join')
