from flask import request
from flask_restful import Resource, marshal, reqparse
from werkzeug.exceptions import Forbidden, NotFound
from controllers.inner_api import api
import services
from controllers.console.datasets.error import DatasetNameDuplicateError
from controllers.console.wraps import setup_required
from controllers.inner_api.wraps import inner_api_only
from fields.dataset_fields import dataset_detail_fields
from libs.helper import uuid_value
from services.account_service import AccountService
from services.external_knowledge_service import ExternalDatasetService


def _validate_name(name):
    if not name or len(name) < 1 or len(name) > 100:
        raise ValueError("Name must be between 1 to 100 characters.")
    return name


def _validate_description_length(description):
    if description and len(description) > 400:
        raise ValueError("Description cannot exceed 400 characters.")
    return description


class ExternalApiTemplateListApi(Resource):
    @setup_required
    @inner_api_only
    def get(self):
        page = request.args.get("page", default=1, type=int)
        limit = request.args.get("limit", default=20, type=int)
        search = request.args.get("keyword", default=None, type=str)
        tenant_id = request.args.get("tenant_id", default=None, type=str)

        external_knowledge_apis, total = ExternalDatasetService.get_external_knowledge_apis(
            page, limit, tenant_id, search
        )
        response = {
            "data": [item.to_dict() for item in external_knowledge_apis],
            "has_more": len(external_knowledge_apis) == limit,
            "limit": limit,
            "total": total,
            "page": page,
        }
        return response, 200

    @setup_required
    @inner_api_only
    def post(self):
        parser = reqparse.RequestParser()
        parser.add_argument(
            "name",
            nullable=False,
            required=True,
            help="Name is required. Name must be between 1 to 100 characters.",
            type=_validate_name,
        )
        parser.add_argument(
            "settings",
            type=dict,
            location="json",
            nullable=False,
            required=True,
        )
        parser.add_argument('tenant_id', type=uuid_value, required=True, location='json')
        parser.add_argument('user_id', type=str, required=True, nullable=False, location='json')
        args = parser.parse_args()

        ExternalDatasetService.validate_api_list(args["settings"])

        # The role of the current user in the ta table must be admin, owner, or editor, or dataset_operator
        current_user = AccountService.get_account_by_name(args["user_id"])
        current_user.current_tenant_id = args["tenant_id"]
        if not current_user.is_dataset_editor:
            raise Forbidden()

        try:
            external_knowledge_api = ExternalDatasetService.create_external_knowledge_api(
                tenant_id=args["tenant_id"], user_id=current_user.id, args=args
            )
        except services.errors.dataset.DatasetNameDuplicateError:
            raise DatasetNameDuplicateError()

        return external_knowledge_api.to_dict(), 201


class ExternalApiTemplateApi(Resource):
    @setup_required
    @inner_api_only
    def get(self, external_knowledge_api_id):
        external_knowledge_api_id = str(external_knowledge_api_id)
        external_knowledge_api = ExternalDatasetService.get_external_knowledge_api(external_knowledge_api_id)
        if external_knowledge_api is None:
            raise NotFound("API template not found.")

        return external_knowledge_api.to_dict(), 200


class ExternalDatasetCreateApi(Resource):
    @setup_required
    @inner_api_only
    def post(self):

        parser = reqparse.RequestParser()
        parser.add_argument("external_knowledge_api_id", type=str, required=True, nullable=False, location="json")
        parser.add_argument("external_knowledge_id", type=str, required=True, nullable=False, location="json")
        parser.add_argument(
            "name",
            nullable=False,
            required=True,
            help="name is required. Name must be between 1 to 100 characters.",
            type=_validate_name,
        )
        parser.add_argument("description", type=str, required=False, nullable=True, location="json")
        parser.add_argument("external_retrieval_model", type=dict, required=False, location="json")
        parser.add_argument('tenant_id', type=uuid_value, required=True, location='json')
        parser.add_argument('user_id', type=str, required=True, nullable=False, location='json')
        args = parser.parse_args()

        # The role of the current user in the ta table must be admin, owner, or editor, or dataset_operator
        current_user = AccountService.get_account_by_name(args["user_id"])
        current_user.current_tenant_id = args["tenant_id"]
        if not current_user.is_dataset_editor:
            raise Forbidden()
        try:
            dataset = ExternalDatasetService.create_external_dataset(
                tenant_id=args["tenant_id"],
                user_id=current_user.id,
                args=args,
            )
        except services.errors.dataset.DatasetNameDuplicateError:
            raise DatasetNameDuplicateError()

        return marshal(dataset, dataset_detail_fields), 201


api.add_resource(ExternalDatasetCreateApi, "/enterprise/datasets/external")
api.add_resource(ExternalApiTemplateListApi, "/enterprise/datasets/external-knowledge-api")
api.add_resource(ExternalApiTemplateApi, "/enterprise/datasets/external-knowledge-api/<uuid:external_knowledge_api_id>")
