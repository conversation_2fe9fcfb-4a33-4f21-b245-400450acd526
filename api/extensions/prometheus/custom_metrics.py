import os
import threading
import time

from flask import Flask
from prometheus_client import Collector<PERSON><PERSON><PERSON><PERSON>, Gauge
from prometheus_client.registry import REGISTRY

from extensions.ext_redis import redis_client
from extensions.ext_database import db


class MetricUpdater:
    def __init__(self):
        self.tasks = []

    def register_task(self, interval, func, *args, **kwargs):
        """
        注册一个定时任务
        :param interval: 任务执行的时间间隔（秒）
        :param func: 执行的函数
        :param args: 函数的位置参数
        :param kwargs: 函数的关键字参数
        """
        self.tasks.append((interval, func, args, kwargs))

    def start(self):
        for interval, func, args, kwargs in self.tasks:
            thread = threading.Thread(target=self._run_task, args=(interval, func, *args), kwargs=kwargs)
            thread.daemon = True
            thread.start()

    @staticmethod
    def _run_task(interval, func, *args, **kwargs):
        while True:
            func(*args, **kwargs)
            time.sleep(interval)


class CustomCollector:

    def __init__(self, registry: CollectorRegistry = REGISTRY):
        self._metric_updater = MetricUpdater()
        self._registry = registry

    def register_thread_metrics(self):
        # 注册线程指标
        _active_threads_gauge = Gauge('python_active_threads', 'Number of active threads in Flask application', registry=self._registry)
        self._metric_updater.register_task(5, lambda : _active_threads_gauge.set(threading.active_count()))

        # 注册 gevent 指标
        if (flask_debug := os.environ.get("FLASK_DEBUG", "0")) and flask_debug.lower() in {"false", "0", "no"}:
            import gevent
            # 注册 gevent 指标
            _active_greenlets_gauge = Gauge('python_active_greenlets', 'Number of active greenlets in Flask application', registry=self._registry)
            self._metric_updater.register_task(5, lambda : _active_greenlets_gauge.set(gevent.hub.get_hub().loop.activecnt))


    def register_redis_metrics(self):
        # 注册 Redis 指标
        max_connections_gauge = Gauge('redis_max_connections', 'Maximum number of connections to Redis', registry=self._registry)
        self._metric_updater.register_task(5, lambda : max_connections_gauge.set(redis_client.connection_pool.max_connections))

        created_connections_gauge = Gauge('redis_created_connections', 'Number of connections created by Redis', registry=self._registry)
        self._metric_updater.register_task(5, lambda : created_connections_gauge.set(redis_client.connection_pool._created_connections))

        in_use_connections_gauge = Gauge('redis_in_use_connections', 'Number of connections in use by Redis', registry=self._registry)
        self._metric_updater.register_task(5, lambda : in_use_connections_gauge.set(len(redis_client.connection_pool._in_use_connections)))

        idle_connections_gauge = Gauge('redis_available_connections', 'Number of connections available to Redis', registry=self._registry)
        self._metric_updater.register_task(5, lambda : idle_connections_gauge.set(len(redis_client.connection_pool._available_connections)))

    def register_sqlalchemy_metrics(self, app: Flask):
        with app.app_context():
            # 注册 SQLAlchemy 指标
            pool = db.engine.pool

            pool_size_gauge = Gauge('sqlalchemy_pool_size', 'Size of the SQLAlchemy connection pool', registry=self._registry)
            self._metric_updater.register_task(5, lambda : pool_size_gauge.set(pool.size()))

            checked_in_connections_gauge = Gauge('sqlalchemy_checked_in_connections', 'Number of connections checked into the SQLAlchemy connection pool', registry=self._registry)
            self._metric_updater.register_task(5, lambda : checked_in_connections_gauge.set(pool.checkedin()))

            checked_out_connections_gauge = Gauge('sqlalchemy_checked_out_connections', 'Number of connections checked out of the SQLAlchemy connection pool', registry=self._registry)
            self._metric_updater.register_task(5, lambda : checked_out_connections_gauge.set(pool.checkedout()))

            overflow_connections_gauge = Gauge('sqlalchemy_overflow_connections', 'Number of overflow connections in the SQLAlchemy connection pool', registry=self._registry)
            overflow = pool.overflow()
            if overflow is None or overflow < 0:
                overflow = 0
            self._metric_updater.register_task(5, lambda : overflow_connections_gauge.set(overflow))

    def start(self):
        self._metric_updater.start()

