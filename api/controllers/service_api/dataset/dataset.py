import logging
import time
import uuid

from flask import request
from flask_restful import marshal, reqparse  # type: ignore
from werkzeug.exceptions import NotFound

import services.dataset_service
from controllers.console.datasets.hit_testing_base import DatasetsHitTestingBase
from controllers.service_api import api
from controllers.service_api.dataset.error import DatasetInUseError, DatasetNameDuplicateError
from controllers.service_api.wraps import DatasetApiResource
from core.model_runtime.entities.model_entities import ModelType
from core.provider_manager import ProviderManager
from core.rag.datasource.retrieval_service import RetrievalService, default_retrieval_model
from core.utils.document_helpers import process_documents_with_permissions
from fields.dataset_fields import dataset_detail_fields
from fields.hit_testing_fields import hit_testing_record_fields
from libs.login import current_user
from models import Account, EndUser, db
from models.dataset import Dataset, DatasetPermissionEnum, DatasetQuery
from services.dataset_service import DatasetService


def _validate_name(name):
    if not name or len(name) < 1 or len(name) > 40:
        raise ValueError("Name must be between 1 to 40 characters.")
    return name


class DatasetListApi(DatasetApiResource):
    """Resource for datasets."""

    def get(self, tenant_id):
        """Resource for getting datasets."""

        page = request.args.get("page", default=1, type=int)
        limit = request.args.get("limit", default=20, type=int)
        # provider = request.args.get("provider", default="vendor")
        search = request.args.get("keyword", default=None, type=str)
        tag_ids = request.args.getlist("tag_ids")
        include_all = request.args.get("include_all", default="false").lower() == "true"

        datasets, total = DatasetService.get_datasets(
            page, limit, tenant_id, current_user, search, tag_ids, include_all
        )
        # check embedding setting
        provider_manager = ProviderManager()
        configurations = provider_manager.get_configurations(tenant_id=current_user.current_tenant_id)

        embedding_models = configurations.get_models(model_type=ModelType.TEXT_EMBEDDING, only_active=True)

        model_names = []
        for embedding_model in embedding_models:
            model_names.append(f"{embedding_model.model}:{embedding_model.provider.provider}")

        data = marshal(datasets, dataset_detail_fields)
        for item in data:
            if item["indexing_technique"] == "high_quality":
                item_model = f"{item['embedding_model']}:{item['embedding_model_provider']}"
                if item_model in model_names:
                    item["embedding_available"] = True
                else:
                    item["embedding_available"] = False
            else:
                item["embedding_available"] = True
        response = {"data": data, "has_more": len(datasets) == limit, "limit": limit, "total": total, "page": page}
        return response, 200

    def post(self, tenant_id):
        """Resource for creating datasets."""
        parser = reqparse.RequestParser()
        parser.add_argument(
            "name",
            nullable=False,
            required=True,
            help="type is required. Name must be between 1 to 40 characters.",
            type=_validate_name,
        )
        parser.add_argument(
            "description",
            type=str,
            nullable=True,
            required=False,
            default="",
        )
        parser.add_argument(
            "indexing_technique",
            type=str,
            location="json",
            choices=Dataset.INDEXING_TECHNIQUE_LIST,
            help="Invalid indexing technique.",
        )
        parser.add_argument(
            "permission",
            type=str,
            location="json",
            choices=(DatasetPermissionEnum.ONLY_ME, DatasetPermissionEnum.ALL_TEAM, DatasetPermissionEnum.PARTIAL_TEAM),
            help="Invalid permission.",
            required=False,
            nullable=False,
        )
        parser.add_argument(
            "external_knowledge_api_id",
            type=str,
            nullable=True,
            required=False,
            default="_validate_name",
        )
        parser.add_argument(
            "provider",
            type=str,
            nullable=True,
            required=False,
            default="vendor",
        )
        parser.add_argument(
            "external_knowledge_id",
            type=str,
            nullable=True,
            required=False,
        )
        args = parser.parse_args()

        try:
            dataset = DatasetService.create_empty_dataset(
                tenant_id=tenant_id,
                name=args["name"],
                description=args["description"],
                indexing_technique=args["indexing_technique"],
                account=current_user,
                permission=args["permission"],
                provider=args["provider"],
                external_knowledge_api_id=args["external_knowledge_api_id"],
                external_knowledge_id=args["external_knowledge_id"],
            )
        except services.errors.dataset.DatasetNameDuplicateError:
            raise DatasetNameDuplicateError()

        return marshal(dataset, dataset_detail_fields), 200


class DatasetApi(DatasetApiResource):
    """Resource for dataset."""

    def delete(self, _, dataset_id):
        """
        Deletes a dataset given its ID.

        Args:
            dataset_id (UUID): The ID of the dataset to be deleted.

        Returns:
            dict: A dictionary with a key 'result' and a value 'success'
                  if the dataset was successfully deleted. Omitted in HTTP response.
            int: HTTP status code 204 indicating that the operation was successful.

        Raises:
            NotFound: If the dataset with the given ID does not exist.
        """

        dataset_id_str = str(dataset_id)

        try:
            if DatasetService.delete_dataset(dataset_id_str, current_user):
                return {"result": "success"}, 204
            else:
                raise NotFound("Dataset not found.")
        except services.errors.dataset.DatasetInUseError:
            raise DatasetInUseError()


class RetrieveApi(DatasetApiResource,DatasetsHitTestingBase):
    def post(self, tenant_id, dataset_id):
        dataset_id_str = str(dataset_id)

        dataset = self.get_and_validate_dataset(dataset_id_str)
        args = self.parse_args()
        retrieval_model = args.get("retrieval_model", None)
        if not retrieval_model:
            retrieval_model = dataset.retrieval_model or default_retrieval_model
        user_name = args["user_id"]
        self.hit_testing_args_check(args)
        if dataset.available_document_count == 0 or dataset.available_segment_count == 0:
            return self.build_empty_response(args["query"])
        user, source_from = (
                                self.get_or_create_user(dataset, dataset_id_str, user_name)
                                if user_name
                                else (current_user, 'account')
                            )
        all_documents = self.retrieve(
            dataset=dataset,
            query=args["query"],
            account=user,
            retrieval_model=retrieval_model,
        )

        if dataset.data_source_type == "feishu_import" and dataset.feishu_permission_check == "open":
            all_documents = self.process_documents_with_permission(
                all_documents, dataset, user.id, "remove"
            )

        top_k = retrieval_model.get("top_k", 2)
        if len(all_documents) > top_k > 0:
            all_documents = all_documents[:top_k]

        return self.build_response(dataset, args, all_documents, source_from, user.id)

    def build_response(self, dataset, args, documents, user_from, user_id):
        """构建统一响应格式"""

        dataset_query = DatasetQuery(
            dataset_id=dataset.id,
            content=args["query"],
            source="service-api",
            created_by_role=user_from,
            created_by=user_id
        )
        db.session.add(dataset_query)
        db.session.commit()

        records = RetrievalService.format_retrieval_documents(documents)
        return {
            "query": {"content": args["query"]},
            "records": marshal([record.model_dump() for record in records], hit_testing_record_fields)
        }

    def process_documents_with_permission(self, documents, dataset, user, operation):
        """处理带权限的文档"""
        return process_documents_with_permissions(documents, dataset, user, operation)


    @classmethod
    def build_empty_response(cls, query):
        """构建空数据集响应"""
        return {
            "query": {
                "content": query,
                "tsne_position": {"x": 0, "y": 0},
            },
            "records": [],
        }

    def get_or_create_user(cls, dataset, app_id, username):

        user_account = Account.query.filter_by(name=username).first()
        if user_account:
            return user_account,"account"

        end_user = db.session.query(EndUser).filter(EndUser.name == username).first()
        if end_user:
            return end_user,"end_user"
        # 创建新用户
        end_user = EndUser(
            tenant_id=dataset.tenant_id,
            app_id=app_id,
            name=username,
            type="servers_api",
            is_anonymous=True,
            session_id=str(uuid.uuid4()),
        )
        db.session.add(end_user)
        db.session.commit()
        return end_user,"end_user"

    def retrieve(cls,
                 dataset: Dataset,
                 query: str,
                 account: Account,
                 retrieval_model: dict,
                 ) -> list:
        start = time.perf_counter()
        # get retrieval model , if the model is not setting , using default
        if not retrieval_model:
            retrieval_model = dataset.retrieval_model or default_retrieval_model

        all_documents = RetrievalService.retrieve(
            retrieval_method=retrieval_model.get("search_method", "semantic_search"),
            dataset_id=dataset.id,
            query=query.replace('"', '\\"'),
            top_k=retrieval_model.get("top_k", 2),
            score_threshold=retrieval_model.get("score_threshold", 0.0)
            if retrieval_model["score_threshold_enabled"]
            else 0.0,
            reranking_model=retrieval_model.get("reranking_model", None)
            if retrieval_model["reranking_enable"]
            else None,
            reranking_mode=retrieval_model.get("reranking_mode") or "reranking_model",
            weights=retrieval_model.get("weights", None),
            user_id=account.id,
        )
        end = time.perf_counter()
        logging.debug(f"service-api retrieve in {end - start:0.4f} seconds")
        return all_documents

api.add_resource(DatasetListApi, "/datasets")
api.add_resource(DatasetApi, "/datasets/<uuid:dataset_id>")
api.add_resource(RetrieveApi, "/datasets/<uuid:dataset_id>/retrieve")