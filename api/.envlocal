# Your App secret key will be used for securely signing the session cookie
# Make sure you are changing this key for your deployment with a strong key.
# You can generate a strong key using `openssl rand -base64 42`.
# Alternatively you can set it with `SECRET_KEY` environment variable.
SECRET_KEY=n4FSSGOenTMTrmGtdJOwQxBDAneZ3tWeKEzEB7ecMdRQVKKJDWO3UteA

# Console API base URL
CONSOLE_API_URL=http://127.0.0.1:5001
CONSOLE_WEB_URL=http://127.0.0.1:3000

# Service API base URL
SERVICE_API_URL=http://127.0.0.1:5001

# Web APP base URL
APP_WEB_URL=http://127.0.0.1:3000

# Files URL
FILES_URL=http://127.0.0.1:5001

# celery configuration
CELERY_BROKER_URL=redis://:<EMAIL>:16379/1

# redis configuration
REDIS_HOST=licloud-dify-dev-default-dev.chj.cloud
REDIS_PORT=16379
REDIS_USERNAME=
REDIS_PASSWORD=jPsHzjGU+cHh
REDIS_DB=0

# PostgreSQL database configuration
DB_USERNAME=licloud_dify_rw
DB_PASSWORD='2QDTCEki6dRunIkr'
DB_HOST=licloud-dify-ontest.rdsgvax1sgwxout.rds.bj.baidubce.com
DB_PORT=3306
DB_DATABASE=licloud_dify_dev

# Storage configuration
# use for store upload files, private keys...
# storage type: local, s3, azure-blob
# STORAGE_TYPE=local
# STORAGE_LOCAL_PATH=/chj/dify/storage

# Storage configuration
# use for store upload files, private keys...
# storage type: local, s3, azure-blob
# STORAGE_TYPE=licloud-s3
# STORAGE_LOCAL_PATH=/chj/dify/storage

# S3 Storage configuration
# S3_ENDPOINT=https://s3.bj.bcebos.com
# S3_BUCKET_NAME=liai-ontest
# S3_REGION=bj
# S3_ACCESS_KEY_CONJUR_PATH=Prd_Vault/authn/App_licloud-dify_All/App_licloud-dify_BaiduCloud_AKSK/username
# S3_SECRET_KEY_CONJUR_PATH=Prd_Vault/authn/App_licloud-dify_All/App_licloud-dify_BaiduCloud_AKSK/password


HOSTED_AZURE_OPENAI_ENABLED=false
HOSTED_AZURE_OPENAI_QUOTA_LIMIT=200
HOSTED_AZURE_OPENAI_API_KEY=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJHMXJuelRGdXF5NUhXd1FjeEg0cm9zWmlUOXhUck5IbSJ9.f6K1OTSN0QTjqF9JOMT3RmbPFew_IIJpCvKH6lfuMVk
HOSTED_AZURE_OPENAI_TRIAL_LLM_MODELS=gpt-4-turbo,gpt-35-turbo,gpt-35-turbo-16k,gpt-4,gpt-4-32k
HOSTED_AZURE_OPENAI_API_BASE=http://127.0.0.1:8081/bcs-apihub-ai-proxy-service/apihub


# CORS configuration
WEB_API_CORS_ALLOW_ORIGINS=*
CONSOLE_CORS_ALLOW_ORIGINS=*

# Vector database configuration, support: weaviate, qdrant, milvus, relyt, pgvecto_rs, pgvector
VECTOR_STORE=pgvector

# PGVector configuration
PGVECTOR_HOST=127.0.0.1
PGVECTOR_PORT=5433
PGVECTOR_USER=postgres
PGVECTOR_PASSWORD=difyai123456
PGVECTOR_DATABASE=dify

# Upload configuration
UPLOAD_FILE_SIZE_LIMIT=15
UPLOAD_FILE_BATCH_LIMIT=5
UPLOAD_IMAGE_FILE_SIZE_LIMIT=10

# Model Configuration
MULTIMODAL_SEND_IMAGE_FORMAT=base64

# Mail configuration, support: resend, smtp
MAIL_TYPE=smtp
MAIL_DEFAULT_SEND_FROM=LiAI Demo <<EMAIL>>
RESEND_API_KEY=
RESEND_API_URL=
# smtp configuration
SMTP_SERVER=smtp.feishu.cn
SMTP_PORT=465
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=uZ65OdGzZnujBhsl
SMTP_USE_TLS=true

# Sentry configuration
SENTRY_DSN=

# DEBUG
DEBUG=true
SQLALCHEMY_ECHO=true

# Notion import configuration, support public and internal
NOTION_INTEGRATION_TYPE=public
NOTION_CLIENT_SECRET=you-client-secret
NOTION_CLIENT_ID=you-client-id
NOTION_INTERNAL_SECRET=you-internal-secret

ETL_TYPE=dify
UNSTRUCTURED_API_URL=

SSRF_PROXY_HTTP_URL=http://licloud-dify-ssrf-proxy:3128
SSRF_PROXY_HTTPS_URL=http://licloud-dify-ssrf-proxy:3128

BATCH_UPLOAD_LIMIT=10
KEYWORD_DATA_SOURCE_TYPE=database

# CODE EXECUTION CONFIGURATION
CODE_EXECUTION_ENDPOINT=http://licloud-dify-sendbox:8194
CODE_EXECUTION_API_KEY=Q4793gLSHd1rQGZChcYp5Pcd0ZgQQ2hG4V1UZhqBeckLKiiQ8o++QPNd
CODE_MAX_NUMBER=9223372036854775807
CODE_MIN_NUMBER=-9223372036854775808
CODE_MAX_STRING_LENGTH=80000
TEMPLATE_TRANSFORM_MAX_LENGTH=80000
CODE_MAX_STRING_ARRAY_LENGTH=30
CODE_MAX_OBJECT_ARRAY_LENGTH=30
CODE_MAX_NUMBER_ARRAY_LENGTH=1000

EDITION=CLOUD

## licloud
HOSTED_LICLOUD_TRIAL_ENABLED=true
HOSTED_LICLOUD_TRIAL_QUOTA_LIMIT=1000
HOSTED_LICLOUD_TRIAL_APIHUB_KEY=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJHMXJuelRGdXF5NUhXd1FjeEg0cm9zWmlUOXhUck5IbSJ9.f6K1OTSN0QTjqF9JOMT3RmbPFew_IIJpCvKH6lfuMVk
HOSTED_LICLOUD_TRIAL_DBSTORE_KEY=09zLD38B7XvgjrKEirxtHScgSuTpDO8XzQHaF21EGJm8
HOSTED_LICLOUD_TRIAL_LLM_MODELS=matrix-base-4k,matrix-agent-8k
HOSTED_LICLOUD_TRIAL_EMBEDDING_MODELS=embedding_model_bge_large_zh

# API Tool configuration
API_TOOL_DEFAULT_CONNECT_TIMEOUT=10
API_TOOL_DEFAULT_READ_TIMEOUT=60

# HTTP Node configuration
HTTP_REQUEST_MAX_CONNECT_TIMEOUT=300
HTTP_REQUEST_MAX_READ_TIMEOUT=600
HTTP_REQUEST_MAX_WRITE_TIMEOUT=600
HTTP_REQUEST_NODE_MAX_BINARY_SIZE=10485760 # 10MB
HTTP_REQUEST_NODE_MAX_TEXT_SIZE=1048576 # 1MB


; # IDAAS configuration
; IDAAS_CLIENT_ID=3eOmMCHA8Hk9eNu3hgDvB
; IDAAS_CLIENT_SECRET=eyJrdHkiOiJvY3QiLCJraWQiOiJTMGNXUHlYUXRRIiwiYWxnIjoiSFMyNTYiLCJrIjoiblU1c0VmNGcwTFVqZGtTRnhwYTV6WmgxbEdLY1RGRm95S1FSOUk2Y3RIYyJ9
; IDAAS_AUTH_URL=https://id-ontest.lixiang.com/api/auth
; IDAAS_TOKEN_URL = https://id-ontest.lixiang.com/api/token
; IDAAS_USER_INFO_URL = https://li.test.k8s.chj.cloud/licloud-iam-service/v1/users/profile
; IDAAS_SERVICE_ID = jIfOD2sWV0SUey4gISJgP
; IDAAS_SERVICE_SECRET = eyJrdHkiOiJvY3QiLCJraWQiOiI5OTdmViIsInVzZSI6ImVuYyIsImFsZyI6IkExMjhDQkMtSFMyNTYiLCJrIjoiWXJNREkyLWNkelJoeXA2ZWF1VjY0NUNxMzBhYXVoVjZ1aWpkb0VQM2FJVSJ9
; IDAAS_BASE_URL = https://id-ontest.lixiang.com/api

# IDAAS IAM configuration
IDAAS_CLIENT_ID=3eOmMCHA8Hk9eNu3hgDvB
IDAAS_CLIENT_SECRET=eyJrdHkiOiJvY3QiLCJraWQiOiJTMGNXUHlYUXRRIiwiYWxnIjoiSFMyNTYiLCJrIjoiblU1c0VmNGcwTFVqZGtTRnhwYTV6WmgxbEdLY1RGRm95S1FSOUk2Y3RIYyJ9
IDAAS_AUTH_URL=https://id-ontest.lixiang.com/api/auth
IDAAS_TOKEN_URL = https://id-ontest.lixiang.com/api/token
IDAAS_SERVICE_ID = jIfOD2sWV0SUey4gISJgP
IDAAS_SERVICE_SECRET = eyJrdHkiOiJvY3QiLCJraWQiOiI5OTdmViIsInVzZSI6ImVuYyIsImFsZyI6IkExMjhDQkMtSFMyNTYiLCJrIjoiWXJNREkyLWNkelJoeXA2ZWF1VjY0NUNxMzBhYXVoVjZ1aWpkb0VQM2FJVSJ9
IDAAS_BASE_URL = https://id-ontest.lixiang.com/api
IDAAS_USER_INFO_URL = https://li.test.k8s.chj.cloud/licloud-iam-service/v1/users/profile

IAM_QUERY_USER_URL= https://li.test.k8s.chj.cloud/licloud-iam-service/v1/users/detail
IAM_USER_INFO_URL = https://li.test.k8s.chj.cloud/licloud-iam-service/v1/users/profile

LOG_FILE=/Users/<USER>/Downloads/log/dify11.log
LOG_LEVEL=DEBUG
#INNER API
INNER_API=true
INNER_API_KEY=ZGlmeS1pbm5lci1hcGkta2V5


HOSTED_FETCH_APP_TEMPLATES_MODE=builtin

## TONGYI
#HOSTED_TONGYI_API_BASE=https://apihub-dify-adaptor.ontest.fc.chj.cloud/providers/tongyi/paths
HOSTED_TONGYI_API_BASE=https://apihub-dify-adaptor.ontest.fc.chj.cloud/providers/tongyi/paths
HOSTED_TONGYI_API_KEY=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJHMXJuelRGdXF5NUhXd1FjeEg0cm9zWmlUOXhUck5IbSJ9.f6K1OTSN0QTjqF9JOMT3RmbPFew_IIJpCvKH6lfuMVk
HOSTED_TONGYI_TRIAL_ENABLED=true
HOSTED_TONGYI_TRIAL_QUOTA_LIMIT=1000
HOSTED_TONGYI_TRIAL_LLM_MODELS=qwen-turbo
