import logging
from datetime import datetime, timezone

from cloudevents.http import from_http
from flask import request
from flask_restful import Resource

from constants.languages import languages
from controllers.console.wraps import setup_required
from controllers.inner_api import api
from controllers.inner_api.wraps import inner_api_only
from extensions.ext_database import db
from models.account import AccountStatus
from services.account_service import AccountService, RegisterService
from services.licloud_event_service import LiCloudEventService


class TenantMemberJoinEventAPI(Resource):

    @setup_required
    @inner_api_only
    def post(self):
        event = from_http(request.headers, request.get_data())

        if event.get_attributes()['type'] == 'cloud.chj.li.iam.tenant.members.join':
            LiCloudEventService().tenant_member_join(event)
            return {
                'message': 'tenant member join event processed.'
            }, 200

        return "", 400


class AccountInitAPI(Resource):

    @setup_required
    @inner_api_only
    def post(self):
        try:
            # 尝试从请求体中解析 JSON 数据
            data = request.get_json()

            # 检查 JSON 数据的结构
            if not isinstance(data, list):
                return {'message': 'Data must be a list of dictionaries'}, 400

            total_count = len(data)
            existing_users_count = 0
            failed_users_count = 0

            provider = 'idaas'
            language = 'zh-Hans',

            for item in data:
                if not isinstance(item, dict) or 'username' not in item or 'email' not in item:
                    failed_users_count += 1
                    continue

                username = item['username']
                email = item['email']
                account = AccountService.get_account_by_name(username)
                if account is not None:
                    logging.info(f"AccountInitAPI: account {username} already exists.")
                    existing_users_count += 1
                    continue

                try:

                    db.session.begin_nested()

                    open_id = username

                    account = AccountService.create_account(
                        email=email,
                        name=username,
                        interface_language=language if language else languages[0],
                        password=None
                    )
                    account.status = AccountStatus.ACTIVE.value
                    account.initialized_at = datetime.now(timezone.utc).replace(tzinfo=None)

                    if open_id is not None or provider is not None:
                        AccountService.link_account_integrate(provider, open_id, account)

                    db.session.commit()
                except Exception as e:
                    db.session.rollback()
                    logging.error(f"AccountInitAPI: Failed to register user {username}: {str(e)}")
                    failed_users_count += 1

            return {
                'message': 'Account initialization completed',
                'total_count': total_count,
                'existing_users_count': existing_users_count,
                'failed_users_count': failed_users_count
            }, 200

        except ValueError:
            return {'message': 'Invalid JSON data'}, 400
        except Exception as e:
            logging.error(f"AccountInitAPI: An error occurred: {str(e)}")
            return {'message': str(e)}, 500


api.add_resource(TenantMemberJoinEventAPI, '/enterprise/account/join-ce-receiver')
api.add_resource(AccountInitAPI, '/enterprise/account/init')
