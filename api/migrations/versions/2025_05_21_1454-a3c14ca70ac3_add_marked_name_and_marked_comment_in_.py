"""add marked_name and marked_comment in workflows

Revision ID: a3c14ca70ac3
Revises: 404526a02cb2
Create Date: 2025-05-21 14:54:59.601082

"""
from alembic import op
import models as models
from models import extensions
from models.extensions import FeishuApp
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'a3c14ca70ac3'
down_revision = '404526a02cb2'
branch_labels = None
depends_on = None


def upgrade():
    with op.batch_alter_table('workflows', schema=None) as batch_op:
        batch_op.add_column(sa.Column('marked_name', sa.String(), nullable=False, server_default=''))
        batch_op.add_column(sa.Column('marked_comment', sa.String(), nullable=False, server_default=''))


def downgrade():
    with op.batch_alter_table('workflows', schema=None) as batch_op:
        batch_op.drop_column('marked_comment')
        batch_op.drop_column('marked_name')
