from copy import deepcopy
from typing import Dict, Any, List
import logging
import os
from flask_restful import Resource
from cloudevents.http import from_http
from flask import request

from controllers.inner_api import api
from controllers.inner_api.subscription.utils import ToolsClient
from controllers.inner_api.wraps import inner_api_only
from controllers.console.wraps import setup_required
from licloud.apihub import DeployEnv

from .shared.models import SubscriptionEvent, ProjectInfo, ProjectType
from .shared.processor import SubscriptionEventProcessor
from services.tools.mcp_tools_mange_service import MCPToolManageService
from .utils import get_mcp_details_by_code

logger = logging.getLogger(__name__)

# 全局依赖
li_env = os.environ.get('LI_ENV', DeployEnv.PROD)
tools = ToolsClient()

# MCP服务类型映射
MCP_SERVICE_TYPES = {
    'mcp-claude': {'type': 'chat', 'provider': 'anthropic'},
    'mcp-gpt': {'type': 'chat', 'provider': 'openai'},
    'mcp-search': {'type': 'tool', 'provider': 'search'},
    'mcp-database': {'type': 'tool', 'provider': 'database'},
    'mcp-file': {'type': 'tool', 'provider': 'file_system'},
    'mcp-code': {'type': 'tool', 'provider': 'code_execution'}
}


def handle_mcp_subscription_approved(event: SubscriptionEvent) -> Dict[str, Any]:
    """处理MCP服务订阅审核通过"""
    event_id = event.id
    data = event.data
    logger.info(f"[{event_id}] 🎉 MCP subscription approved for {data.username}@{data.tenant_id}")

    try:
        # 获取租户ID
        tenant_id = tools.query_db_tenant_id(app_name=data.app)
        if not tenant_id:
            logger.error(f"[{event_id}] Failed to get tenant_id for app: {data.app}")
            raise ValueError(f"No tenant found for app: {data.app}")

        # 获取API凭证
        credentials = get_mcp_api_credentials(data.tokens, li_env)
        if not credentials:
            logger.error(f"[{event_id}] Failed to get MCP API credentials")
            raise ValueError("No valid MCP API credentials found")

        # 处理每个MCP服务
        processed_services = []
        failed_services = []

        for project in data.projects:
            service_code = project.code
            logger.info(f"[{event_id}] Processing MCP service: {project.name} ({service_code})")

            try:
                process_mcp_service(
                    event_id=event_id,
                    user_name=data.username,
                    tenant_id=tenant_id,
                    credentials=deepcopy(credentials),
                    project=project
                )
                processed_services.append(service_code)
                logger.info(f"[{event_id}] ✅ Successfully processed MCP service: {service_code}")

            except Exception as e:
                logger.error(f"[{event_id}] ❌ Failed to process MCP service {service_code}: {e}")
                failed_services.append({"service_code": service_code, "error": str(e)})

        logger.info(f"[{event_id}] ✅ MCP subscription processing completed. "
                    f"Success: {len(processed_services)}, Failed: {len(failed_services)}")

        return {
            "status": "approved",
            "service_type": "mcp",
            "event_id": event_id,
            "audit_id": data.audit_id,
            "process_id": data.process_id,
            "username": data.username,
            "tenant_id": tenant_id,
            "app": data.app,
            "processed_services": processed_services,
            "failed_services": failed_services,
        }

    except Exception as e:
        logger.error(f"[{event_id}] 💥 Error in MCP subscription approval handler: {e}")
        raise


def get_mcp_api_credentials(tokens, target_env) -> Dict[str, str]:
    """获取MCP API凭证"""
    env_key = target_env if target_env == DeployEnv.PROD else DeployEnv.ONTEST
    token = tokens.get_token_for_env(env_key)

    if token:
        return {
            "api_key": token,
        }
    return {}


def process_mcp_service(event_id: str, user_name: str, tenant_id: str, credentials: Dict, project: ProjectInfo):

    
    service_code = project.code
    service_name = project.name

    if not service_code:
        logger.warning(f"[{event_id}] Empty service code, skip")
        return

    logger.info(f"[{event_id}] 🔍 Getting MCP details for service code: {service_code}")
    
    try:
        # 1. 获取 MCP 详情信息
        mcp_details = get_mcp_details_by_code(service_code)
        if not mcp_details:
            logger.error(f"[{event_id}] Failed to get MCP details for service: {service_code}")
            raise ValueError(f"无法获取 MCP 服务详情: {service_code}")
        
        # 从详情信息中提取字段
        description = mcp_details.get('description')
        server_url = mcp_details.get('server_url')
        author = mcp_details.get('author')
        
        logger.info(f"[{event_id}] 📋 MCP details retrieved - Name: {service_name}, Author: {author}")
        logger.info(f"[{event_id}] 💾 Saving MCP service subscription using MCPToolManageService: {service_code}")
        
        # 2. 保存 MCP 订阅
        mcp_subscription = MCPToolManageService.create_mcp_subscription(
            tenant_id=tenant_id,
            user_name=user_name,
            server_url=server_url,
            code=service_code,
            name=service_name,
            description=description,
            author=author,
            credentials=credentials or {}
        )

        logger.info(f"[{event_id}] ✅ Successfully created MCP subscription: {service_code} (ID: {mcp_subscription.id})")
        
    except Exception as e:
        logger.error(f"[{event_id}] ❌ Failed to save MCP subscription: {e}")
        raise




class MCPSubscription(Resource):
    """MCP服务订阅API"""

    def __init__(self):
        # 处理所有应用类型的MCP订阅
        self.processor = SubscriptionEventProcessor(
            approved_handler=handle_mcp_subscription_approved,
            project_type_filter=ProjectType.AI_MCP
        )

    @setup_required
    @inner_api_only
    def post(self):
        """处理MCP服务订阅事件"""
        event_id = None
        try:
            # 解析原始事件
            raw_event = from_http(request.headers, request.get_data())
            event_id = raw_event.get('id', 'unknown')

            logger.info(f"[{event_id}] 📨 Received MCP subscription event, "
                        f"source={raw_event.get('source')}, type={raw_event.get('type')}")

            # 解析为结构化事件
            event = self.processor.parse_event(raw_event)
            if event is None:
                logger.debug(f"[{event_id}] MCP subscription event filtered out, skipping processing")
                return "", 200

            # 处理MCP订阅事件
            result = self.processor.process(event)
            logger.info(f"[{event_id}] ✅ MCP subscription event processing completed")

            return {"success": True, "data": result}, 200

        except Exception as e:
            logger.error(f"[{event_id or 'unknown'}] 💥 Unexpected error processing MCP subscription event: {e}")
            return {"error": "Internal server error"}, 500


# 注册API路由
api.add_resource(MCPSubscription, '/enterprise/mcp/subscriptions')