import json

import click

from core.rag.datasource.vdb.vector_factory import Vector
from extensions.ext_database import db
from models.dataset import Dataset, DatasetCollectionBinding
from models.model import AppAnnotationSetting, App


@click.command("migrate-vdb-from-pgvector-to-adbpg", help="迁移向量数据库")
@click.option("--from-type", help="源数据库类型，如：pgvector_rs, adbpg 等", required=True)
@click.option("--to-type", help="目标数据库类型，如：pgvector_rs, adbpg 等", required=True)
@click.option("--dataset-id", help="数据集ID，全部迁移时写 all", required=True)
def migrate_vdb(from_type, to_type, dataset_id):
    # 迁移指定数据集
    if dataset_id != "all":
        dataset = (
            db.session.query(Dataset)
            .filter(Dataset.id == dataset_id)
            .first()
        )
        if not dataset:
            click.echo(click.style(f"数据集 {dataset_id} 不存在", fg="red"))
            return
        if dataset.index_struct_dict["type"] != from_type:
            click.echo(click.style(f"数据集 {dataset.id} 的向量数据库类型不是 {from_type}", fg="red"))
            return
        _migrate_vdb_of_dataset(from_type, to_type, dataset)
        click.echo(click.style(f"数据集 {dataset.id} 迁移完成\n>>>>>>>>>>>>>>>>>>>>\n\n", fg="green"))
        return

    # 迁移所有数据集
    click.echo(click.style(f"开始迁移所有数据集", fg="green"))
    datasets = (
        db.session.query(Dataset)
        .filter(Dataset.indexing_technique == "high_quality")
        .all()
    )
    click.echo(click.style(f"共有 {len(datasets)} 个数据集", fg="green"))
    migrated_knowledge_count = 0
    for dataset in datasets:
        try:
            if dataset.index_struct_dict["type"] != from_type:
                click.echo(click.style(f"数据集 {dataset.id} 的向量数据库类型不是 {from_type}, 忽略", fg="yellow"))
                continue
            _migrate_vdb_of_dataset(from_type, to_type, dataset)
            migrated_knowledge_count += 1
            click.echo(click.style("---\n", fg="green"))
        except Exception as e:
            click.echo(click.style(f"数据集 {dataset.id} 迁移失败：{str(e)}", fg="red"))
    click.echo(click.style(f"迁移完成, 共 {migrated_knowledge_count}/{len(datasets)} 个数据集迁移成功\n>>>>>>>>>>>>>>>>>>>>\n\n", fg="green"))

    # 迁移所有标注数据集
    click.echo(click.style(f"开始迁移所有标注数据集", fg="green"))
    app_annotation_settings = db.session.query(AppAnnotationSetting).all()
    click.echo(click.style(f"共有 {len(app_annotation_settings)} 个标注数据集", fg="green"))
    migrated_count = 0
    for app_annotation_setting in app_annotation_settings:
        dataset_collection_binding = (
            db.session.query(DatasetCollectionBinding)
            .filter(DatasetCollectionBinding.id == app_annotation_setting.collection_binding_id)
            .first()
        )
        if not dataset_collection_binding:
            click.echo(click.style(f"标注数据集 {app_annotation_setting.id} 的绑定数据集不存在", fg="yellow"))
            continue
        app = db.session.query(App).filter(App.id == app_annotation_setting.app_id).first()
        if not app:
            click.echo(click.style(f"标注数据集 {app_annotation_setting.id} 的应用不存在", fg="yellow"))
            continue
        dataset = Dataset(
            id=app.id,
            tenant_id=app.tenant_id,
            indexing_technique="high_quality",
            embedding_model_provider=dataset_collection_binding.provider_name,
            embedding_model=dataset_collection_binding.model_name,
            collection_binding_id=dataset_collection_binding.id,
            index_struct=json.dumps({"type": from_type, "vector_store": {"class_prefix": Dataset.gen_collection_name_by_id(app.id).lower()}}),
        )
        from_vector = _create_base_vector(dataset, attributes=["doc_id", "annotation_id", "app_id"])
        if from_vector is None or from_vector.get_type() != from_type:
            click.echo(click.style(f"标注数据集 {app_annotation_setting.id} 的向量数据库类型不是 {from_type}", fg="red"))
            continue
        index_struct_dict = dataset.index_struct_dict
        index_struct_dict["type"] = to_type
        dataset.index_struct = json.dumps(index_struct_dict)
        to_vector = _create_base_vector(dataset, attributes=["doc_id", "annotation_id", "app_id"])
        if to_vector is None or to_vector.get_type() != to_type:
            click.echo(click.style(f"标注数据集 {app_annotation_setting.id} 的向量数据库类型不是 {to_type}", fg="red"))
            continue
        try:
            to_vector.delete()
            click.echo(click.style(f"删除 {app_annotation_setting.id} @ {to_type} 类型数据库成功", fg="green"))
        except Exception as e:
            click.echo(click.style(f"删除 {app_annotation_setting.id} @ {to_type} 类型数据库失败：{str(e)}", fg="red"))
            continue
        record_count = from_vector.migrate_to(to_vector)
        migrated_count += 1
        click.echo(click.style(f"标注数据集 {app_annotation_setting.id} 迁移完成，共迁移 {record_count} 条数据", fg="green"))

    click.echo(click.style(f"所有标注数据集迁移完成, 共迁移 {migrated_count} 个数据集", fg="green"))



def _migrate_vdb_of_dataset(from_type: str, to_type: str, dataset: Dataset):
    """
    迁移指定数据集的向量数据库到 to_type 类型
    """

    click.echo(click.style(f"数据集 {dataset.id}({dataset.name}) 开始迁移为 {to_type} 类型", fg="green"))

    from_vector = _create_base_vector(dataset)
    if from_vector is None or from_vector.get_type() != from_type:
        click.echo(click.style(f"数据集 {dataset.id} 的向量数据库类型不是 {from_type}", fg="red"))
        return
    index_struct_dict = dataset.index_struct_dict
    index_struct_dict["type"] = to_type
    dataset.index_struct = json.dumps(index_struct_dict)
    to_vector = _create_base_vector(dataset)
    if to_vector is None or to_vector.get_type() != to_type:
        click.echo(click.style(f"数据集 {dataset.id} 的目标库未能转为 {to_type} 类型", fg="red"))
        return
    try:
        to_vector.delete()
        click.echo(click.style(f"数据集 {dataset.id} @ {to_type} 删除成功", fg="green"))
    except Exception as e:
        click.echo(click.style(f"数据集 {dataset.id} @ {to_type} 删除失败：{str(e)}", fg="red"))
        return

    record_count = from_vector.migrate_to(to_vector)
    db.session.add(dataset)
    db.session.commit()
    click.echo(click.style(f"数据集 {dataset.id} 迁移完成，共迁移 {record_count} 条数据", fg="green"))


def _create_base_vector(dataset:Dataset, attributes=None):
    """
    创建向量数据库
    """
    vectory_type = dataset.index_struct_dict["type"]
    vector_factory_cls = Vector.get_vector_factory(vectory_type)
    return vector_factory_cls().init_vector(dataset, attributes, None)