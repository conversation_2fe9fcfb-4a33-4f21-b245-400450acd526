from flask import request
from flask_restful import Resource, marshal, marshal_with, fields, abort, reqparse

from controllers.inner_api import api
from controllers.console.wraps import setup_required
from controllers.inner_api.wraps import inner_api_only

from core.model_runtime.entities.model_entities import ModelType
from core.provider_manager import ProviderManager
from extensions.ext_database import db
from fields.dataset_fields import dataset_detail_fields
from libs.helper import TimestampField, uuid_value
from models import ApiToken
from services.account_service import AccountService
from services.dataset_service import DatasetPermissionService, DatasetService
from werkzeug.exceptions import Forbidden, NotFound

api_key_fields = {
    "id": fields.String,
    "type": fields.String,
    "token": fields.String,
    "last_used_at": TimestampField,
    "created_at": TimestampField,
}

api_key_list = {"data": fields.List(fields.Nested(api_key_fields), attribute="items")}


def _validate_name(name):
    if not name or len(name) < 1 or len(name) > 40:
        raise ValueError("Name must be between 1 to 40 characters.")
    return name


def _validate_description_length(description):
    if len(description) > 400:
        raise ValueError("Description cannot exceed 400 characters.")
    return description


class EnterpriseDatasetListApi(Resource):
    @setup_required
    @inner_api_only
    def get(self):
        page = request.args.get("page", default=1, type=int)
        limit = request.args.get("limit", default=20, type=int)
        # provider = request.args.get("provider", default="vendor")
        search = request.args.get("keyword", default=None, type=str)
        tag_ids = request.args.getlist("tag_ids")
        user_id = request.args.get("user_id", default=None, type=str)
        tenant_id = request.args.get("tenant_id", default=None, type=str)

        current_user = AccountService.get_account_by_name(user_id)
        current_user.current_tenant_id = tenant_id

        datasets, total = DatasetService.get_datasets(
            page, limit, current_user.current_tenant_id, current_user, search, tag_ids
        )

        # check embedding setting
        provider_manager = ProviderManager()
        configurations = provider_manager.get_configurations(tenant_id=current_user.current_tenant_id)

        embedding_models = configurations.get_models(model_type=ModelType.TEXT_EMBEDDING, only_active=True)

        model_names = []
        for embedding_model in embedding_models:
            model_names.append(f"{embedding_model.model}:{embedding_model.provider.provider}")

        data = marshal(datasets, dataset_detail_fields)
        for item in data:
            if item["indexing_technique"] == "high_quality":
                item_model = f"{item['embedding_model']}:{item['embedding_model_provider']}"
                if item_model in model_names:
                    item["embedding_available"] = True
                else:
                    item["embedding_available"] = False
            else:
                item["embedding_available"] = True

            if item.get("permission") == "partial_members":
                part_users_list = DatasetPermissionService.get_dataset_partial_member_list(item["id"])
                item.update({"partial_member_list": part_users_list})
            else:
                item.update({"partial_member_list": []})

        response = {"data": data, "has_more": len(datasets) == limit, "limit": limit, "total": total, "page": page}
        return response, 200



class EnterpriseDatasetRelatedAppListApi(Resource):
    @setup_required
    @inner_api_only
    def get(self, dataset_id):
        dataset_id_str = str(dataset_id)
        dataset = DatasetService.get_dataset(dataset_id_str)
        if dataset is None:
            raise NotFound("Dataset not found.")

        app_dataset_joins = DatasetService.get_related_apps(dataset.id)

        related_apps = []
        for app_dataset_join in app_dataset_joins:
            app_model = app_dataset_join.app
            if app_model:
                related_apps.append(app_model)

        return {"data": related_apps, "total": len(related_apps)}, 200

class EnterpriseDatasetApiKeyApi(Resource):
    max_keys = 10
    token_prefix = "dataset-"
    resource_type = "dataset"

    @setup_required
    @inner_api_only
    @marshal_with(api_key_list)
    def get(self):
        tenant_id = request.args.get("tenant_id", default=None, type=str)
        keys = (
            db.session.query(ApiToken)
            .filter(ApiToken.type == self.resource_type, ApiToken.tenant_id == tenant_id)
            .all()
        )
        return {"items": keys}

    @setup_required
    @inner_api_only
    @marshal_with(api_key_fields)
    def post(self):
        parser = reqparse.RequestParser()
        parser.add_argument('tenant_id', type=uuid_value, required=True, location='json')
        parser.add_argument('user_id', type=str, required=True, nullable=False, location='json')
        args = parser.parse_args()
        user_id = args['user_id']
        tenant_id = args['tenant_id']
        current_user = AccountService.get_account_by_name(user_id)
        current_user.current_tenant_id = tenant_id

        # The role of the current user in the ta table must be admin or owner
        if not current_user.is_admin_or_owner:
            raise Forbidden()

        current_key_count = (
            db.session.query(ApiToken)
            .filter(ApiToken.type == self.resource_type, ApiToken.tenant_id == current_user.current_tenant_id)
            .count()
        )

        if current_key_count >= self.max_keys:
            abort(
                400,
                message=f"Cannot create more than {self.max_keys} API keys for this resource type.",
                code="max_keys_exceeded",
            )

        key = ApiToken.generate_api_key(self.token_prefix, 24)
        api_token = ApiToken()
        api_token.tenant_id = current_user.current_tenant_id
        api_token.token = key
        api_token.type = self.resource_type
        db.session.add(api_token)
        db.session.commit()
        return api_token, 200


api.add_resource(EnterpriseDatasetListApi, "/enterprise/datasets")
api.add_resource(EnterpriseDatasetApiKeyApi, "/enterprise/datasets/api-keys")
api.add_resource(EnterpriseDatasetRelatedAppListApi, "/enterprise/datasets/<uuid:dataset_id>/related-apps")
