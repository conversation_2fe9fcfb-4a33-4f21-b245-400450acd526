from timeit import default_timer
from flask import current_app, request, make_response
from prometheus_client import Counter, Histogram, Summary
from prometheus_flask_exporter.multiprocess import GunicornInternalPrometheusMetrics

try:
    # try to convert http.HTTPStatus to int status codes
    from http import HTTPStatus

    def _to_status_code(response_status):
        if isinstance(response_status, HTTPStatus):
            return response_status.value
        else:
            return response_status
except ImportError:
    # otherwise simply use the status as is
    def _to_status_code(response_status):
        return response_status


class LicloudMetrics(GunicornInternalPrometheusMetrics):

    def __init__(self, app=None, path='/metrics', **kwargs):
        super().__init__(app=app, path=path, **kwargs)

    def export_defaults(self, buckets=None, group_by='path',
                        latency_as_histogram=True,
                        prefix='flask', app=None, **kwargs):
        """
        Export the default metrics:
            - HTTP request latencies
            - HTTP request exceptions
            - Number of HTTP requests

        :param buckets: the time buckets for request latencies
            (will use the default when `None`)
        :param group_by: group default HTTP metrics by
            this request property, like `path`, `endpoint`, `rule`, etc.
            (defaults to `path`)
        :param latency_as_histogram: export request latencies
            as a Histogram, otherwise use a Summary instead
            (defaults to `True` to export as a Histogram)
        :param prefix: prefix to start the default metrics names with
            or `NO_PREFIX` (to skip prefix)
        :param app: the Flask application
        """

        if app is None:
            app = self.app or current_app

        if not prefix:
            prefix = self._defaults_prefix or 'flask'

        if group_by:
            duration_group = group_by
        else:
            duration_group = 'path'

        if callable(duration_group):
            duration_group_name = duration_group.__name__

        else:
            duration_group_name = duration_group

        if prefix == "#no_prefix":
            prefix = ""
        else:
            prefix = prefix + "_"

        try:
            self.info(
                '%sexporter_info' % prefix,
                'Information about the Prometheus Flask exporter',
                version=self.version,
                impl_class=self.__class__.__name__
            )
        except ValueError:
            return  # looks like we have already exported the default metrics



        labels = self._get_combined_labels(None)

        if latency_as_histogram:
            # use the default buckets from prometheus_client if not given here
            buckets_as_kwargs = {}
            if buckets is not None:
                buckets_as_kwargs['buckets'] = buckets

            request_duration_metric = Histogram(
                '%shttp_server_requests_seconds' % prefix,
                'Flask HTTP request duration in seconds',
                ('method', 'uri', 'status') + labels.keys(),
                registry=self.registry,
                **buckets_as_kwargs
            )

        else:
            # export as Summary instead
            request_duration_metric = Summary(
                '%shttp_server_requests_seconds' % prefix,
                'Flask HTTP request duration in seconds',
                ('method', 'uri', 'status') + labels.keys(),
                registry=self.registry
            )

        counter_labels = ('method', 'status') + labels.keys()
        request_total_metric = Counter(
            '%shttp_server_requests_total' % prefix,
            'Total number of HTTP requests',
            counter_labels,
            registry=self.registry
        )

        request_exceptions_metric = Counter(
            '%shttp_server_requests_exceptions_total' % prefix,
            'Total number of HTTP requests which resulted in an exception',
            counter_labels,
            registry=self.registry
        )

        def before_request():
            request.prom_start_time = default_timer()

        def after_request(response):
            if hasattr(request, 'prom_do_not_track') or hasattr(request, 'prom_exclude_all'):
                return response

            if self.excluded_paths:
                if any(pattern.match(request.path) for pattern in self.excluded_paths):
                    return response

            if hasattr(request, 'prom_start_time') and self._not_yet_handled('duration_reported'):
                total_time = max(default_timer() - request.prom_start_time, 0)

                if callable(duration_group):
                    group = duration_group(request)
                else:
                    group = getattr(request, duration_group)

                request_duration_labels = {
                    'method': request.method,
                    'status': _to_status_code(response.status_code),
                    'uri': group
                }
                request_duration_labels.update(labels.values_for(response))

                request_duration_metric.labels(**request_duration_labels).observe(total_time)

            if self._not_yet_handled('total_reported'):
                request_total_metric.labels(
                    method=request.method, status=_to_status_code(response.status_code),
                    **labels.values_for(response)
                ).inc()

            return response

        def teardown_request(exception=None):
            if not exception or hasattr(request, 'prom_do_not_track') or hasattr(request, 'prom_exclude_all'):
                return

            if self.excluded_paths:
                if any(pattern.match(request.path) for pattern in self.excluded_paths):
                    return

            response = make_response('Exception: %s' % exception, 500)

            if callable(duration_group):
                group = duration_group(request)
            else:
                group = getattr(request, duration_group)

            request_exceptions_metric.labels(
                method=request.method, status=500,
                **labels.values_for(response)
            ).inc()

            if hasattr(request, 'prom_start_time') and self._not_yet_handled('duration_reported'):
                total_time = max(default_timer() - request.prom_start_time, 0)

                request_duration_labels = {
                    'method': request.method,
                    'status': 500,
                    duration_group_name: group
                }
                request_duration_labels.update(labels.values_for(response))

                request_duration_metric.labels(**request_duration_labels).observe(total_time)

            if self._not_yet_handled('total_reported'):
                request_total_metric.labels(
                    method=request.method, status=500,
                    **labels.values_for(response)
                ).inc()

            return

        app.before_request(before_request)
        app.after_request(after_request)
        app.teardown_request(teardown_request)