
from flask_restful import Resource, reqparse
from controllers.inner_api.wraps import inner_api_only
from controllers.inner_api import api
from controllers.console.wraps import setup_required
from core.model_runtime.entities.model_entities import ModelType
from core.model_runtime.errors.validate import CredentialsValidateFailedError
from services.model_load_balancing_service import ModelLoadBalancingService
from services.model_provider_service import ModelProviderService


class ModelProviderModelApi(Resource):

    @setup_required
    @inner_api_only
    def post(self, provider: str):

        parser = reqparse.RequestParser()
        parser.add_argument('tenant_id', type=str, required=True, nullable=False, location='json')
        parser.add_argument('model', type=str, required=True, nullable=False, location='json')
        parser.add_argument('model_type', type=str, required=True, nullable=False,
                            choices=[mt.value for mt in ModelType], location='json')
        parser.add_argument('credentials', type=dict, required=False, nullable=True, location='json')
        parser.add_argument('load_balancing', type=dict, required=False, nullable=True, location='json')
        parser.add_argument('config_from', type=str, required=False, nullable=True, location='json')
        args = parser.parse_args()

        tenant_id = args['tenant_id']
        model_load_balancing_service = ModelLoadBalancingService()

        if ('load_balancing' in args and args['load_balancing'] and
                'enabled' in args['load_balancing'] and args['load_balancing']['enabled']):
            if 'configs' not in args['load_balancing']:
                raise ValueError('invalid load balancing configs')

            # save load balancing configs
            model_load_balancing_service.update_load_balancing_configs(
                tenant_id=tenant_id,
                provider=provider,
                model=args['model'],
                model_type=args['model_type'],
                configs=args['load_balancing']['configs']
            )

            # enable load balancing
            model_load_balancing_service.enable_model_load_balancing(
                tenant_id=tenant_id,
                provider=provider,
                model=args['model'],
                model_type=args['model_type']
            )
        else:
            # disable load balancing
            model_load_balancing_service.disable_model_load_balancing(
                tenant_id=tenant_id,
                provider=provider,
                model=args['model'],
                model_type=args['model_type']
            )

            if args.get('config_from', '') != 'predefined-model':
                model_provider_service = ModelProviderService()

                try:
                    model_provider_service.save_model_credentials(
                        tenant_id=tenant_id,
                        provider=provider,
                        model=args['model'],
                        model_type=args['model_type'],
                        credentials=args['credentials']
                    )
                except CredentialsValidateFailedError as ex:
                    raise ValueError(str(ex))

        return {'result': 'success'}, 200


api.add_resource(ModelProviderModelApi, '/enterprise/workspaces/model-providers/<string:provider>/models')
