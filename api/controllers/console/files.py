from typing import Literal

from flask import request
from flask_login import current_user  # type: ignore
from flask_restful import Resource, marshal_with  # type: ignore
from werkzeug.exceptions import Forbidden
from core.file import FileTransferMethod, helpers as file_helpers
import services
from configs import dify_config
from constants import DOCUMENT_EXTENSIONS
from controllers.common.errors import FilenameNotExistsError
from controllers.console.wraps import (
    account_initialization_required,
    cloud_edition_billing_resource_check,
    setup_required,
)
from core.file import FileTransferMethod
from factories import file_factory
from fields.file_fields import file_fields, upload_config_fields, file_fields_v1
from libs.login import login_required
from services.file_service import FileService

from .error import (
    FileTooLargeError,
    NoFileUploadedError,
    TooManyFilesError,
    UnsupportedFileTypeError,
)

PREVIEW_WORDS_LIMIT = 3000


class FileApi(Resource):
    @setup_required
    @login_required
    @account_initialization_required
    @marshal_with(upload_config_fields)
    def get(self):
        return {
            "file_size_limit": dify_config.UPLOAD_FILE_SIZE_LIMIT,
            "batch_count_limit": dify_config.UPLOAD_FILE_BATCH_LIMIT,
            "image_file_size_limit": dify_config.UPLOAD_IMAGE_FILE_SIZE_LIMIT,
            "video_file_size_limit": dify_config.UPLOAD_VIDEO_FILE_SIZE_LIMIT,
            "audio_file_size_limit": dify_config.UPLOAD_AUDIO_FILE_SIZE_LIMIT,
            "workflow_file_upload_limit": dify_config.WORKFLOW_FILE_UPLOAD_LIMIT,
        }, 200

    @setup_required
    @login_required
    @account_initialization_required
    @marshal_with(file_fields_v1)
    @cloud_edition_billing_resource_check("documents")
    def post(self):
        file = request.files["file"]
        source_str = request.form.get("source")
        return_file_str = request.form.get("return_file", "false")
        return_file = return_file_str.lower() == "true"
        source: Literal["datasets"] | None = "datasets" if source_str == "datasets" else None

        if "file" not in request.files:
            raise NoFileUploadedError()

        if len(request.files) > 1:
            raise TooManyFilesError()

        if not file.filename:
            raise FilenameNotExistsError

        if source == "datasets" and not current_user.is_dataset_editor:
            raise Forbidden()

        if source not in ("datasets", None):
            source = None

        try:
            upload_file = FileService.upload_file(
                filename=file.filename,
                content=file.read(),
                mimetype=file.mimetype,
                user=current_user,
                source=source,
            )
        except services.errors.file.FileTooLargeError as file_too_large_error:
            raise FileTooLargeError(file_too_large_error.description)
        except services.errors.file.UnsupportedFileTypeError:
            raise UnsupportedFileTypeError()

        response = upload_file
        preview_url = file_helpers.get_signed_file_url(upload_file_id=upload_file.id)
        # If return_file is True, create a File object and include it in the response
        if return_file:
            # Add the file object to the response
            response = {
                "id": upload_file.id,
                "name": upload_file.name,
                "size": upload_file.size,
                "extension": upload_file.extension,
                "mime_type": upload_file.mime_type,
                "created_by": upload_file.created_by,
                "created_at": upload_file.created_at,
                "file": {
                    "upload_file_id": upload_file.id,
                    "transfer_method": FileTransferMethod.LOCAL_FILE.value,
                    "tenant_id": current_user.current_tenant_id
                }
            }

        return response, 201


class FilePreviewApi(Resource):
    @setup_required
    @login_required
    @account_initialization_required
    def get(self, file_id):
        file_id = str(file_id)
        text = FileService.get_file_preview(file_id)
        return {"content": text}


class FilePreviewUrlApi(Resource):
    @setup_required
    @login_required
    @account_initialization_required
    def get(self, file_id):
        return {"url": FileService.get_file_preview_url(str(file_id))}


class FileSupportTypeApi(Resource):
    @setup_required
    @login_required
    @account_initialization_required
    def get(self):
        return {"allowed_extensions": DOCUMENT_EXTENSIONS}
