from enum import Enum
from typing import Any, Literal, Union

from pydantic import BaseModel

from core.agent.entities import AgentToolEntity
from core.agent.strategy.entities import AgentRuntime
from core.app.entities.app_invoke_entities import InvokeFrom
from core.prompt.entities.advanced_prompt_entities import MemoryConfig
from core.tools.entities.tool_entities import ToolSelector, McpSelector
from core.tools.tool.tool import Tool
from core.tools.tool_manager import ToolManager
from core.workflow.nodes.base.entities import BaseNodeData
from core.workflow.nodes.tool.entities import ToolEntity


class AgentNodeData(BaseNodeData):
    agent_strategy_provider_name: str  # redundancy
    agent_strategy_name: str
    agent_strategy_label: str  # redundancy
    memory: MemoryConfig | None = None

    class AgentInput(BaseModel):
        value: Union[list[str], list[ToolSelector], list[McpSelector], Any]
        type: Literal["mixed", "variable", "constant"]

    agent_parameters: dict[str, AgentInput]


class ParamsAutoGenerated(Enum):
    CLOSE = 0
    OPEN = 1


class AgentOldVersionModelFeatures(Enum):
    """
    Enum class for old SDK version llm feature.
    """

    TOOL_CALL = "tool-call"
    MULTI_TOOL_CALL = "multi-tool-call"
    AGENT_THOUGHT = "agent-thought"
    VISION = "vision"
    STREAM_TOOL_CALL = "stream-tool-call"
    DOCUMENT = "document"
    VIDEO = "video"
    AUDIO = "audio"

class WorkflowAgentRuntime(AgentRuntime):
    workflow_id: str
    node_id: str

    def get_tool_runtime(self, tool_entity: AgentToolEntity) -> Tool:
        return ToolManager().get_workflow_tool_runtime(
            tenant_id=self.tenant_id,
            app_id=self.app_id,
            workflow_id=self.workflow_id,
            node_id=self.node_id,
            workflow_tool=ToolEntity(
                provider_id=tool_entity.provider_id,
                provider_type=tool_entity.provider_type,
                provider_name=tool_entity.provider_id,
                tool_name=tool_entity.tool_name,
                tool_label=tool_entity.tool_name,
                tool_configurations=tool_entity.tool_parameters,
            ),
            invoke_from=self.invoke_from,
        )


class WorkflowAgentToolEntity(AgentToolEntity):
    pass