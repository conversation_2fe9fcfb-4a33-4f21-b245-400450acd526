"""add feishuApp model

Revision ID: b54dccdf64eb
Revises: c0a88b58d135
Create Date: 2024-12-11 02:04:32.994588

"""
from alembic import op
import models as models
from models import extensions
from models.extensions import FeishuApp
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'b54dccdf64eb'
down_revision = 'c0a88b58d135'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('app_extensions',
    sa.Column('id', models.types.StringUUID(), server_default=sa.text('uuid_generate_v4()'), nullable=False),
    sa.Column('app_id', models.types.StringUUID(), nullable=False),
    sa.Column('short_id', sa.String(length=255), nullable=False),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP(0)'), nullable=False),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP(0)'), nullable=False),
    sa.PrimaryKeyConstraint('id', name='app_extension_pkey')
    )
    with op.batch_alter_table('app_extensions', schema=None) as batch_op:
        batch_op.create_index('app_extension_app_id_idx', ['app_id'], unique=False)

    op.create_table('feishu_apps',
    sa.Column('id', models.types.StringUUID(), server_default=sa.text('uuid_generate_v4()'), nullable=False),
    sa.Column('app_id', models.types.StringUUID(), nullable=False),
    sa.Column('feishu_app_id', sa.String(length=255), nullable=False),
    sa.Column('feishu_app_secret', sa.String(length=2048), nullable=False),
    sa.Column('feishu_app_name', sa.String(length=255), nullable=False),
    sa.Column('encrypt_key', sa.String(length=2048), nullable=True),
    sa.Column('verification_token', sa.String(length=2048), nullable=True),
    sa.Column('call_app_inputs_json_path', sa.JSON(), nullable=True),
    sa.Column('enable_encryption', sa.Boolean(), server_default=sa.text('false'), nullable=False),
    sa.Column('enable_verification', sa.Boolean(), server_default=sa.text('false'), nullable=False),
    sa.Column('enable_prologue', sa.Boolean(), server_default=sa.text('false'), nullable=False),
    sa.Column('enable_reference', sa.Boolean(), server_default=sa.text('false'), nullable=False),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP(0)'), nullable=False),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP(0)'), nullable=False),
    sa.Column('created_by', models.types.StringUUID(), nullable=True),
    sa.Column('updated_by', models.types.StringUUID(), nullable=True),
    sa.PrimaryKeyConstraint('id', name='feishu_app_pkey')
    )
    with op.batch_alter_table('feishu_apps', schema=None) as batch_op:
        batch_op.create_index('feishu_app_app_id_idx', ['app_id'], unique=False)

    op.create_table('feishu_im_messages',
    sa.Column('id', models.types.StringUUID(), server_default=sa.text('uuid_generate_v4()'), nullable=False),
    sa.Column('message', sa.JSON(), nullable=False),
    sa.Column('call_app_inputs', sa.JSON(), nullable=True),
    sa.Column('reply_message_id', models.types.StringUUID(), nullable=True),
    sa.Column('reply_at', sa.DateTime(), nullable=True),
    sa.Column('reply_read_at', sa.DateTime(), nullable=True),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP(0)'), nullable=False),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP(0)'), nullable=False),
    sa.PrimaryKeyConstraint('id', name='feishu_im_message_pkey')
    )


    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###

    op.drop_table('feishu_im_messages')
    with op.batch_alter_table('feishu_apps', schema=None) as batch_op:
        batch_op.drop_index('feishu_app_app_id_idx')

    op.drop_table('feishu_apps')
    with op.batch_alter_table('app_extensions', schema=None) as batch_op:
        batch_op.drop_index('app_extension_app_id_idx')

    op.drop_table('app_extensions')
    # ### end Alembic commands ###
