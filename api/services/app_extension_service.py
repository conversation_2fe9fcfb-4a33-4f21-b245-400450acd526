import logging

from core.workflow.nodes.http_request.executor import _generate_random_string
from extensions.ext_database import db
from models.extensions import AppExtension


class AppExtensionService:

    @staticmethod
    def generate_app_short_id(app_id: str) -> AppExtension:
        max_attempts = 3
        for attempt in range(max_attempts):
            short_id = _generate_random_string(8)
            if not db.session.query(AppExtension).filter(AppExtension.short_id == short_id).first():
                app_extension = AppExtension()
                app_extension.app_id = app_id
                app_extension.short_id = short_id
                db.session.add(app_extension)
                db.session.commit()
                return app_extension

            logging.warning(f"Collision detected for short_id: {short_id}, retrying...")

        raise ValueError("Failed to generate a unique short_id after multiple attempts")

    @staticmethod
    def get_app_short_id(app_id: str) -> str:
        app_extension = db.session.query(AppExtension).filter(AppExtension.app_id == app_id).first()
        if not app_extension:
            app_extension = AppExtensionService.generate_app_short_id(app_id)

        return app_extension.short_id if app_extension else None

    @staticmethod
    def get_by_short_id(short_id: str) -> AppExtension:
        return db.session.query(AppExtension).filter(AppExtension.short_id == short_id).first()
