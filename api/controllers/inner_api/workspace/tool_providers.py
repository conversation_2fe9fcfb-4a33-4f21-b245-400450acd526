from flask_restful import Resource, reqparse
from sqlalchemy.orm import Session

from controllers.inner_api.wraps import inner_api_only

from controllers.inner_api import api
from controllers.console.wraps import setup_required
from services.tools.builtin_tools_manage_service import BuiltinToolManageService
from services.account_service import AccountService
from extensions.ext_database import db


class ToolBuiltinProviderUpdateApi(Resource):
    @setup_required
    @inner_api_only
    def post(self, provider):
        parser = reqparse.RequestParser()
        parser.add_argument('credentials', type=dict, required=True, nullable=False, location='json')
        parser.add_argument('tenant_id', type=str, required=True, nullable=False, location='json')
        parser.add_argument('username', type=str, required=True, nullable=False, location='json')

        args = parser.parse_args()

        operator = AccountService.get_account_by_name(args['username'])

        with Session(db.engine) as session:
            result = BuiltinToolManageService.update_builtin_tool_provider(
                session=session,
                user_id=operator.id,
                tenant_id=args["tenant_id"],
                provider_name=provider,
                credentials=args["credentials"],
            )
            session.commit()
        return result


# builtin tool provider
api.add_resource(ToolBuiltinProviderUpdateApi, '/enterprise/workspaces/tool-provider/builtin/<provider>/update')
