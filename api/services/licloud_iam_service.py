import logging

from flask import current_app
from werkzeug.exceptions import InternalServerError, Unauthorized
from cachetools import cached, TTLCache

from libs.httpx import httpx_client
from models.account import Account, Tenant

logger = logging.getLogger(__name__)

class LiCloudIamService:
    def __init__(self):
        # self.base_url = current_app.config.get('IDAAS_BASE_URL')
        # self.service_id = current_app.config.get('IDAAS_SERVICE_ID')
        # self.service_secret = current_app.config.get('IDAAS_SERVICE_SECRET')
        self.user_info_url = current_app.config.get('IAM_USER_INFO_URL')
        self.query_user_url = current_app.config.get('IAM_QUERY_USER_URL')
        self.licloud_api_url = current_app.config.get('LICLOUD_API_URL')

    @cached(cache=TTLCache(maxsize=1024, ttl=60))
    def get_account_id(self, token: str) -> str:
        headers = {'Authorization': f"Bearer {token}"}
        response = httpx_client.get(self.user_info_url, headers=headers)

        if response.status_code != 200:
            raise Unauthorized('Invalid token')
        userInfo = response.json()

        return Account.get_id_by_openid(provider="idaas", open_id=userInfo['data']['username'])

    def get_user_info_by_username(self, username) -> dict:
        """
        Get user info by username
        :param username: str
        :return: dict
        return example:
            {
                "avatar": "string",
                "department": {
                  "companyId": 0,
                  "departmentCode": "string",
                  "fullName": "string",
                  "id": 0,
                  "idPath": "string",
                  "leaderStaffId": 0,
                  "level": 0,
                  "namePath": "string",
                  "parentId": 0
                },
                "departmentId": 0,
                "email": "string",
                "feishuId": "string",
                "feishuOpenId": "string",
                "foreignAid": true,
                "id": 0,
                "jobNumber": "string",
                "mobile": "string",
                "name": "string",
                "sex": "string",
                "subjectName": "string",
                "type": "SERVICE",
                "username": "string"
          }
        """

        url = f"{self.query_user_url}/{username}"
        response = httpx_client.get(url=url)
        if response.status_code != 200:
            logging.warning(f"Failed to get user info by username: {username} response code: {response.status_code}")
            raise InternalServerError(
                f"Failed to get user info by username: {username} response code: {response.status_code}")

        return response.json()['data']

    def is_admin_of_licloud_tenant(self, account: Account, tenant: Tenant) -> bool:
        """
        Check if the account is the admin of the tenant
        :param account: Account
        :param tenant: Tenant
        :return: bool
        """

        custom_config = tenant.custom_config_dict
        if not custom_config or 'licloud_tenant_id' not in custom_config:
            return False

        li_tid = custom_config['licloud_tenant_id']
        username = account.name

        return (
                self.is_owner_of_tenant(li_tid, username)
                or self.is_member_of_group(li_tid, 'TENANT_ADMINISTRATOR', username)
                or self.is_member_of_group(li_tid, 'TENANT_SRE_GROUP', username)
        )

    def is_member_of_group(self, tid: str, group_name: str, username: str) -> bool:
        """
        Check if the user is a member of the group
        :param tid: str 融合云租户ID，六位小写字母组成
        :param group_name: str 用户组名称
        :param username: str 用户名
        :return: bool
        """

        logger.debug(f"Check if the user is member of group: {{ tid: {tid}, group_name: {group_name}, username: {username} }}")
        try:
            url = f"{self.licloud_api_url}/licloud-iam-service/v1/tenants/{tid}/groups/{group_name}/members/{username}"
            response = httpx_client.get(url=url)
            logger.debug(f"Check if the user is member of group response: {response.status_code} {response.text}")
            return response.status_code == 200
        except Exception as e:
            logging.error(f"Failed to check if the user is a member of the group: {e}")
            return False

    def is_owner_of_tenant(self, tid: str, username: str) -> bool:
        """
        Check if the user is the owner of the tenant
        :param tid: str
        :param username: str
        :return: bool
        """

        logger.debug(f"Check if the user is owner of tenant: {{ tid: {tid}, username: {username} }}")
        try:
            url = f"{self.licloud_api_url}/licloud-iam-service/v1/tenants/{tid}"
            response = httpx_client.get(url=url)
            logger.debug(f"Check if the user is owner of tenant response: {response.status_code} {response.text}")
            return response.json()['data']['owner'] == username
        except Exception as e:
            logging.error(f"Failed to check if the user is the owner of the tenant: {e}")
            return False