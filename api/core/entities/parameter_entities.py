import enum
from enum import StrEnum
from typing import Any


class CommonParameterType(StrEnum):
    SECRET_INPUT = "secret-input"
    TEXT_INPUT = "text-input"
    SELECT = "select"
    STRING = "string"
    NUMBER = "number"
    FILE = "file"
    FILES = "files"
    SYSTEM_FILES = "system-files"
    BOOLEAN = "boolean"
    APP_SELECTOR = "app-selector"
    MODEL_SELECTOR = "model-selector"
    TOOLS_SELECTOR = "array[tools]"
    # MCP object and array type parameters
    ARRAY ="array"
    OBJECT = "object"



class AppSelectorScope(StrEnum):
    ALL = "all"
    CHAT = "chat"
    WORKFLOW = "workflow"
    COMPLETION = "completion"


class ModelSelectorScope(StrEnum):
    LLM = "llm"
    TEXT_EMBEDDING = "text-embedding"
    RERANK = "rerank"
    TTS = "tts"
    SPEECH2TEXT = "speech2text"
    MODERATION = "moderation"
    VISION = "vision"


class ToolSelectorScope(StrEnum):
    ALL = "all"
    CUSTOM = "custom"
    BUILTIN = "builtin"
    WORKFLOW = "workflow"


def as_normal_type(typ: enum.StrEnum):
    if typ.value in {
        CommonParameterType.SECRET_INPUT,
        CommonParameterType.SELECT,
    }:
        return "string"
    return typ.value

def cast_parameter_value(typ: enum.StrEnum, value: Any, /):
    try:
        match typ.value:
            case CommonParameterType.STRING | CommonParameterType.SECRET_INPUT | CommonParameterType.SELECT:
                if value is None:
                    return ""
                else:
                    return value if isinstance(value, str) else str(value)

            case CommonParameterType.BOOLEAN:
                if value is None:
                    return False
                elif isinstance(value, str):
                    # Allowed YAML boolean value strings: https://yaml.org/type/bool.html
                    # and also '0' for False and '1' for True
                    match value.lower():
                        case "true" | "yes" | "y" | "1":
                            return True
                        case "false" | "no" | "n" | "0":
                            return False
                        case _:
                            return bool(value)
                else:
                    return value if isinstance(value, bool) else bool(value)

            case CommonParameterType.NUMBER:
                if isinstance(value, int | float):
                    return value
                elif isinstance(value, str) and value:
                    if "." in value:
                        return float(value)
                    else:
                        return int(value)
                elif value is None:
                    return 0
                else:
                    raise ValueError(f"The tool parameter value {value} is not in correct type of {typ.value}.")
            case CommonParameterType.SYSTEM_FILES | CommonParameterType.FILES:
                if not isinstance(value, list):
                    return [value]
                return value
            case CommonParameterType.FILE:
                if isinstance(value, list):
                    if len(value) != 1:
                        raise ValueError(
                            "This parameter only accepts one file but got multiple files while invoking.")
                    else:
                        return value[0]
                return value
            case CommonParameterType.MODEL_SELECTOR | CommonParameterType.APP_SELECTOR:
                if not isinstance(value, dict):
                    raise ValueError("The selector must be a dictionary.")
                return value
            case CommonParameterType.TOOLS_SELECTOR:
                if value and not isinstance(value, list):
                    raise ValueError("The tools selector must be a list.")
                return value
            case CommonParameterType.ARRAY:
                if not isinstance(value, list):
                    # Try to parse JSON string for arrays
                    if isinstance(value, str):
                        try:
                            import json

                            parsed_value = json.loads(value)
                            if isinstance(parsed_value, list):
                                return parsed_value
                        except (json.JSONDecodeError, ValueError):
                            pass
                    return [value]
                return value
            case CommonParameterType.OBJECT:
                if not isinstance(value, dict):
                    # Try to parse JSON string for objects
                    if isinstance(value, str):
                        try:
                            import json

                            parsed_value = json.loads(value)
                            if isinstance(parsed_value, dict):
                                return parsed_value
                        except (json.JSONDecodeError, ValueError):
                            pass
                    return {}
                return value
            case _:
                return str(value)
    except ValueError:
        raise
    except Exception:
        raise ValueError(f"The tool parameter value {value} is not in correct type of {typ.value}.")