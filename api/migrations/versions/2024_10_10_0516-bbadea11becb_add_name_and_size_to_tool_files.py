"""add name and size to tool_files

Revision ID: bbadea11becb
Revises: 33f5fac87f29
Create Date: 2024-10-10 05:16:14.764268

"""
from alembic import op
import models as models
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'bbadea11becb'
down_revision = 'd8e744d88ed6'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    # Get the database connection
    conn = op.get_bind()
    
    # Use SQLAlchemy inspector to get the columns of the 'tool_files' table
    inspector = sa.inspect(conn)
    columns = [col['name'] for col in inspector.get_columns('tool_files')]

    # If 'name' or 'size' columns already exist, exit the upgrade function
    if 'name' in columns or 'size' in columns:
        return 

    with op.batch_alter_table('tool_files', schema=None) as batch_op:
        batch_op.add_column(sa.Column('name', sa.String(), nullable=True))
        batch_op.add_column(sa.Column('size', sa.Integer(), nullable=True))
    op.execute("UPDATE tool_files SET name = '' WHERE name IS NULL")
    op.execute("UPDATE tool_files SET size = -1 WHERE size IS NULL")
    with op.batch_alter_table('tool_files', schema=None) as batch_op:
        batch_op.alter_column('name', existing_type=sa.String(), nullable=False)
        batch_op.alter_column('size', existing_type=sa.Integer(), nullable=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('tool_files', schema=None) as batch_op:
        batch_op.drop_column('size')
        batch_op.drop_column('name')
    # ### end Alembic commands ###
