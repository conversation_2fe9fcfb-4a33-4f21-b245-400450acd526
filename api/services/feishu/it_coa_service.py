import logging
from typing import Optional

from flask import current_app

from libs.httpx import httpx_client
from extensions.ext_redis import redis_client

it_coa_cache_token_key = "it_coa_token"
it_coa_cache_feishu_user_id = "it_coa_feishu_user_id"


class ITCoaService:

    def __init__(self):
        self.it_coa_url = current_app.config.get('IT_COA_URL')
        self.it_client_id = current_app.config.get('IT_COA_CLIENT_ID')
        self.it_client_secret = current_app.config.get('IT_COA_CLIENT_SECRET')

    def _get_token(self) -> str:
        if redis_client.exists(it_coa_cache_token_key):
            return redis_client.get(it_coa_cache_token_key).decode('utf-8')

        url = self.it_coa_url + '/oauth/token'
        payload = {
            "grant_type": "client_credentials",
            "client_id": self.it_client_id,
            "client_secret": self.it_client_secret,
            "scope": "*"
        }
        headers = {
            'Content-Type': 'application/json'
        }
        response = httpx_client.request("POST", url, headers=headers, json=payload)
        if response.status_code != 200:
            return None
        resp = response.json()
        expires_in = resp.get('expires_in')
        redis_client.set('it_coa_cache_token_key', resp.get('access_token'), ex=expires_in - 5)
        return resp.get('access_token')

    def search_user(self, search_type, search_text):
        try:
            logging.info(f"search_user info: search_type:{search_type};search_text:{search_text}")

            if not search_type or not search_text:
                return None

            else:
                token = self._get_token()
                url = self.it_coa_url + '/api/info/search_in_all_user'
                headers = {
                    'Authorization': f'Bearer {token}'
                }
                params = {
                    'search_type': search_type,
                    'search_text': search_text
                }

                response = httpx_client.request("GET", url, headers=headers, params=params)

                if response.status_code != 200:
                    return None

                else:
                    return response.json()

        except Exception as e:
            logging.error(f"ERROR search_user : {e}")
            return None

    def get_user_info_by_feishu_user_id(self, user_id) -> Optional[str]:
        try:
            cache_key = f"{it_coa_cache_feishu_user_id}:{user_id}"
            if redis_client.exists(cache_key):
                username: bytes = redis_client.get(cache_key)
                return username.decode('utf-8')

            user = self.search_user(search_type='feishu_user_id', search_text=user_id)
            if not user:
                return None
            username = user['data'][0]['ldap_name']
            if username:
                redis_client.set(cache_key, username, ex=60 * 60 * 24 * 30)
            return username

        except Exception as e:
            logging.warning(f"Failed to get user info by feishu user id: {user_id} error: {e}")
            return None

    def get_user_by_email(self, user_email) -> Optional[str]:
        try:
            logging.info(f"user_email : {user_email}")

            if not user_email:
                return None

            else:
                user = self.search_user(search_type='email', search_text=user_email)

                if not user:
                    return None

                else:
                    username = user['data'][0]['ldap_name']

                    return username

        except Exception as e:
            logging.error(f"ERROR: get_user_by_email user_email:{user_email} error: {e}")
            return None
