import base64
import logging
import os
import time
from collections.abc import Generator
from contextlib import closing

import boto3
from botocore.client import Config
from botocore.exceptions import ClientError

from configs import dify_config
from libs.httpx import httpx_client_without_verify
from extensions.storage.base_storage import BaseStorage

logger = logging.getLogger(__name__)


class LiCloudS3Storage(BaseStorage):
    """Implementation for LiCloud s3 storage.
    """

    def __init__(self):
        super().__init__()
        self.bucket_name = dify_config.S3_BUCKET_NAME

        conjur_client = ConjurClient()
        s3_access_key = dify_config.S3_ACCESS_KEY
        if not s3_access_key:
            s3_access_key = conjur_client.get_secret(dify_config.S3_ACCESS_KEY_CONJUR_PATH)

        s3_secret_key = dify_config.S3_SECRET_KEY
        if not s3_secret_key:
            s3_secret_key = conjur_client.get_secret(dify_config.S3_SECRET_KEY_CONJUR_PATH)

        self.client = boto3.client(
            "s3",
            aws_secret_access_key=s3_secret_key,
            aws_access_key_id=s3_access_key,
            endpoint_url=dify_config.S3_ENDPOINT,
            region_name=dify_config.S3_REGION,
            # config=Config(
            #     s3={"addressing_style": dify_config.S3_ADDRESS_STYLE},
            #     request_checksum_calculation="when_required",
            #     response_checksum_validation="when_required",
            # )
        )

    def save(self, filename, data):
        self.client.put_object(Bucket=self.bucket_name, Key=filename, Body=data)

    def load_once(self, filename: str) -> bytes:
        try:
            with closing(self.client) as client:
                data = client.get_object(Bucket=self.bucket_name, Key=filename)["Body"].read()
        except ClientError as ex:
            if ex.response["Error"]["Code"] == "NoSuchKey":
                raise FileNotFoundError("File not found")
            else:
                raise
        return data

    def load_stream(self, filename: str) -> Generator:
        def generate(filename: str = filename) -> Generator:
            try:
                with closing(self.client) as client:
                    response = client.get_object(Bucket=self.bucket_name, Key=filename)
                    yield from response["Body"].iter_chunks()
            except ClientError as ex:
                if ex.response["Error"]["Code"] == "NoSuchKey":
                    raise FileNotFoundError("File not found")
                else:
                    raise

        return generate()

    def download(self, filename, target_filepath):
        with closing(self.client) as client:
            client.download_file(self.bucket_name, filename, target_filepath)

    def exists(self, filename):
        with closing(self.client) as client:
            try:
                client.head_object(Bucket=self.bucket_name, Key=filename)
                return True
            except:
                return False

    def delete(self, filename):
        self.client.delete_object(Bucket=self.bucket_name, Key=filename)


class ConjurClient:
    def __init__(self,
                 appliance_url="https://sec-conjur-drfollower.inner.chj.cloud",
                 token_file_path="/run/conjur/access-token"):
        self.appliance_url = appliance_url
        self.token_file_path = token_file_path
        self.headers = {}

    def _load_access_token(self):
        while not os.path.exists(self.token_file_path):
            logging.info(f"{self.token_file_path} does not exist")
            print(f"{self.token_file_path} does not exist")
            logging.info("Retrying in 1 second...")
            time.sleep(1)

        with open(self.token_file_path) as file:
            access_token = file.read()

        base64_token = base64.b64encode(access_token.encode("utf-8")).decode("utf-8")

        self.headers = {
            "Authorization": f'Token token="{base64_token}"'
        }

    def _mask_secret(self, secret):
        if len(secret) <= 4:
            return "***"
        else:
            return secret[0] + "***" + secret[-1]

    def get_secret(self, secret_path):
        self._load_access_token()

        secret_url = f"{self.appliance_url}/secrets/lixiang/variable/{secret_path}"
        response = httpx_client_without_verify.get(secret_url, headers=self.headers)

        if response.status_code == 200:
            secret = response.text
            masked_secret = self._mask_secret(secret)
            logging.info(f"Obtained secret for path '{secret_path}': {masked_secret}")
            print(f"Obtained secret for path '{secret_path}': {masked_secret}")
            return response.text
        else:
            logging.error(f"Failed to obtain secret for {secret_path}")
            print(f"Failed to obtain secret for {secret_path}")
            return None
