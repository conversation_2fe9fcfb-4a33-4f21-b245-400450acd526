"""datasets_add_feishu_permission_check

Revision ID: c0a88b58d135
Revises: 730e1d239911
Create Date: 2024-11-13 10:31:11.255508

"""
from alembic import op
import models as models
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'c0a88b58d135'
down_revision = '730e1d239911'
branch_labels = None
depends_on = None


def upgrade():

    with op.batch_alter_table('datasets', schema=None) as batch_op:
        batch_op.add_column(sa.Column('feishu_permission_check', sa.String(length=10), server_default=sa.text("'open'::character varying"), nullable=False))


def downgrade():

    with op.batch_alter_table('datasets', schema=None) as batch_op:
        batch_op.drop_column('feishu_permission_check')
