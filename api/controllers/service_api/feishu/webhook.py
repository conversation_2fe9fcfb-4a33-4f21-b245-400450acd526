import logging

from flask import request
from flask_restful import Resource, reqparse
from lark_oapi import LARK_REQUEST_TIMESTAMP, LARK_REQUEST_NONCE, LARK_REQUEST_SIGNATURE
from flask import Flask, current_app
from werkzeug.exceptions import NotFound, Forbidden

from controllers.service_api.dataset.error import ArchivedDocumentImmutableError
from core.utils.feishu_utils import Feishu
from controllers.service_api import api
from services.feishu.feishu_service import feishu_service


class FeishuWebhookApi(Resource):
    """Resource for feishu webhook."""

    def post(self, app_id):
        """Handle feishu webhook."""
        timestamp = request.headers.get(LARK_REQUEST_TIMESTAMP)
        nonce = request.headers.get(LARK_REQUEST_NONCE)
        signature = request.headers.get(LARK_REQUEST_SIGNATURE)
        try:
            return feishu_service.handle_webhook(request.get_data(), timestamp, nonce, signature, app_id)
        except Exception as e:
            logging.error(f"Error: {e}",exc_info=True)
        return {"status": "received"}


class FeishuCallbackApi(Resource):
    """Resource for Feishu Callback."""

    def get(self):
        callback_id = request.args.get('callback_id')

        if not callback_id or not isinstance(callback_id, str) or not callback_id.strip():
            logging.warning(f"Invalid or missing callback_id: {callback_id}")
            return {"status": "error", "message": "Invalid or missing callback_id"}

        logging.info(f"Handling callback for callback_id: {callback_id}")

        try:
            result = feishu_service.handle_callback(callback_id)
            logging.info(f"Successfully handled callback for callback_id: {callback_id}")
            return result
        except ValueError as ve:
            logging.error(f"ValueError handling callback for callback_id: {callback_id}. Error: {ve}")
            return {"status": "error", "message": "Invalid input data"}
        except Exception as e:
            logging.error(f"Unexpected error handling callback for callback_id: {callback_id}. Error: {e}")
            return {"status": "error", "message": "Failed to handle callback"}

class FeishuAuthApi(Resource):
    """飞书文档授权认证接口"""
    def get(self):
        """检查用户是否已授权飞书文档访问"""
        parser = reqparse.RequestParser()
        parser.add_argument("user_name", type=str, required=True, nullable=False, location="args")
        parser.add_argument("redirect_url", type=str, required=False, nullable=True, location="args")
        args = parser.parse_args()

        user_name = args["user_name"]
        redirect_url = args["redirect_url"]

        feishu = Feishu.get_instance()
        # user_name = feishu.get_user_name(user_id)

        if not user_name:
            return {"status": "error", "message": "无法获取用户信息"}, 404

        result = feishu.authorize_user(user_name, redirect_url)
        return result, 200

class FeishuPermissionApi(Resource):
    """飞书文档管理权限验证接口"""
    def post(self):
        """验证用户对飞书文档的访问权限"""
        parser = reqparse.RequestParser()
        parser.add_argument("name", type=str, required=True, nullable=False, location="json")
        parser.add_argument("document_urls", type=list, required=True, nullable=False, location="json")
        parser.add_argument("action", type=str, default="view", required=False, nullable=False, location="json")
        args = parser.parse_args()

        user_name = args["name"]
        document_urls = args["document_urls"]
        action = args["action"]

        feishu = Feishu.get_instance()
        # user_name = feishu.get_user_name(user_id)

        if not user_name:
            return {"status": "error", "message": "无法获取用户信息"}, 404

        accessible_urls = feishu.fetch_accessible_document_urls(user_name, document_urls, action)
        result = {
            "total": len(document_urls),
            "accessible": len(accessible_urls),
            "urls": accessible_urls
        }
        return result, 200


class FeishuDocumentSyncApi(Resource):
    """飞书文档同步接口"""
    def get(self, dataset_id, document_id):
        from services.dataset_service import DatasetService
        """sync website document."""
        dataset_id = str(dataset_id)
        dataset = DatasetService.get_dataset(dataset_id)
        if not dataset:
            raise NotFound("Dataset not found.")
        document_id = str(document_id)
        from services.dataset_service import DocumentService
        document = DocumentService.get_document(dataset.id, document_id)
        if not document:
            raise NotFound("Document not found.")

        # from libs.login import current_user
        # if document.tenant_id != current_user.current_tenant_id:
        #     raise Forbidden("No permission.")

        # 403 if document is archived
        if DocumentService.check_archived(document):
            raise ArchivedDocumentImmutableError()
        # sync document
        from schedule.sync_feishu_documents_task import document_update_for_document_id

        document_update_for_document_id(dataset_id=dataset_id, document_id=document_id)

        return {"result": "success"}, 200

api.add_resource(FeishuPermissionApi, "/documents/feishu/permission")
api.add_resource(FeishuDocumentSyncApi, "/datasets/<uuid:dataset_id>/documents/<uuid:document_id>/feishu-sync")
api.add_resource(FeishuAuthApi, "/api/feishu/auth")
api.add_resource(FeishuWebhookApi, "/apps/<uuid:app_id>/feishu-webhook")
api.add_resource(FeishuCallbackApi, "/apps/feishu-callback")
