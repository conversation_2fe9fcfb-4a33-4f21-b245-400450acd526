import logging
import os
import re
import uuid
from concurrent.futures import ThreadPoolExecutor
from enum import Enum
from typing import Optional

import httpx
from flask import Flask
from pydantic import BaseModel, Field, computed_field

from libs.httpx import httpx_client
from extensions.ext_database import db
from models.model import App, EndUser
from models.account import Account, TenantAccountJoin
from services.enterprise.enterprise_service import EnterpriseService

logger = logging.getLogger(__name__)

# 内容审查不通过的错误
class SecurityModerationError(Exception):
    pass

class SecurityModerationType(Enum):
    LLM_INPUT = "llm-input"
    LLM_OUTPUT = "llm-output"
    TOOL_INPUT = "tool-input"
    TOOL_OUTPUT = "tool-output"
    CODE_INPUT = "code-input"
    CODE_OUTPUT = "code-output"
    HTTP_INPUT = "http-input"
    HTTP_OUTPUT = "http-output"
    RETRIEVAL_INPUT = "retrieval-input"
    RETRIEVAL_OUTPUT = "retrieval-output"
    WHOLE_INPUT = "whole-input"
    WHOLE_OUTPUT = "whole-output"


class ModerationContent(BaseModel):
    """待审查的内容

    Attributes:
        scenario: 审查场景，tool-input, tool-output, code-input, code-output 等
        business_id: 业务ID，用于区分不同业务，如：liai-builder@@tool@@tool_id
        app_id: 应用ID，用于区分不同应用
        app_type: 应用类型，如：agent, workflow
        app_name: 一般对应 OAM 组件名
        owner_id: 应用所有者ID
        owner_username: 应用所有者域账号
        message_id: 消息ID
        user_id: 用户ID
        user_username: 用户域账号
        invoke_from: 调用来源，如：service-api, web-app, explore, debugger
        content: 内容
        sensitive: 是否敏感
    """

    scenario: SecurityModerationType
    business_id: str
    app_id: str
    app_type: str
    app_name: Optional[str] = None
    owner_id: Optional[str] = None
    owner_username: Optional[str] = None
    message_id: Optional[str] = None
    user_id: str
    user_username: Optional[str] = None
    invoke_from: str
    content: str
    sensitive: bool = False


class ModerationResultRiskReasonPosition(BaseModel):
    """
    审查风险原因位置
    """
    tip: str
    position: list[list[int]]


class ModerationResultRiskReason(BaseModel):
    """
    审查风险原因
    """
    risk_tips: str = Field("", alias="riskTips")
    risk_words: str = Field("", alias="riskWords")
    ad_nums: str = Field("", alias="adNums")
    customized_words: str = Field("", alias="customizedWords")
    customized_libs: str = Field("", alias="customizedLibs")
    risk_positions: list[ModerationResultRiskReasonPosition] = Field([], alias="riskPosition")


class ModerationResultRisk(BaseModel):
    """
    审查风险
    """
    type: str
    result: str
    labels: Optional[str]
    reason: ModerationResultRiskReason
    engine: str


class ModerationResult(BaseModel):
    """
    审查结果
    """

    code: int
    result: str
    risk_list: Optional[list[ModerationResultRisk]]
    _message: str = None

    @computed_field
    @property
    def message(self) -> str:
        if self._message:
            return self._message

        new_message = ""
        for risk in self.risk_list:
            new_message += f"{risk.reason.risk_tips};"
        self._message = new_message
        return new_message

    @message.setter
    def message(self, value: str):
        self._message = value


class SecurityModeration:
    """
    安全审查
    """
    app: Flask
    pool: ThreadPoolExecutor
    api_url: str
    api_key: str
    timeout: int
    started: bool = False

    def init_app(self, app: Flask):
        self.app = app
        config = app.config
        self.pool = ThreadPoolExecutor(
            max_workers=config.get("SECURITY_MODERATION_MAX_WORKERS"),
            thread_name_prefix="SecurityModeration"
        )
        self.api_url = config.get("SECURITY_MODERATION_API_URL")
        self.api_key = config.get("SECURITY_MODERATION_API_KEY")
        self.timeout = config.get("SECURITY_MODERATION_TIMEOUT")

    def moderation(self, content: ModerationContent, /, *, sync: bool = False) -> ModerationResult:
        """
        审查内容，默认为异步执行。sync 为 True 时同步执行，否则根据配置判定是否异步执行。

        Args:
            content: 审查内容
            sync: 是否同步执行，默认为 False

        Returns:
            审查结果，异步审查时立即返回通过结果

        Configs:
            SECURITY_MODERATION_SYNC_ALL: 是否全局启用同步审查，默认为 False
            SECURITY_MODERATION_SYNC_APPS: 启用同步审查的应用列表，默认为空
            SECURITY_MODERATION_SYNC_SCENARIOS: 启用同步审查的场景列表，默认为空
        """

        if not content.message_id:
            content.message_id = str(uuid.uuid4())

        try:
            if sync:
                return self._moderation_sync(content)

            # check if sync is enabled globally
            if self.app.config.get("SECURITY_MODERATION_SYNC_ALL", False):
                return self._moderation_sync(content)

            # check if sync is enabled for this app
            if content.app_id and EnterpriseService.is_sync_moderation(content.app_id):
                return self._moderation_sync(content)

            # check if sync is enabled for this scenario
            if content.scenario in self.app.config.get("SECURITY_MODERATION_SYNC_SCENARIOS", []):
                return self._moderation_sync(content)

            return self._moderation_async(content)
        except SecurityModerationError as e:
            logger.warning(f"Security moderation blocked: {e}, content: {content}")
            raise e
        except Exception as e:
            logger.warning(f"Failed to moderation security: {e}, content: {content}")
            return SecurityModeration._default_pass_result(f"Moderation security failed, ignore and pass immediately: {e}")


    def _moderation_async(self, content: ModerationContent) -> ModerationResult:
        """
        异步执行内容审查
        """

        logger.debug(f"Moderation security async: message_id = {content.message_id}")
        self.pool.submit(self._moderation_sync, content)
        return SecurityModeration._default_pass_result("Moderation service is async mode, pass immediately")

    def _moderation_sync(self, content: ModerationContent) -> ModerationResult:
        """
        审查内容
        """

        if os.environ.get("SECURITY_MODERATION_DISABLED", "false").lower() == "true":
            return SecurityModeration._default_pass_result("Moderation service is disabled, pass immediately")

        logger.debug(f"Moderation: message_id = {content.message_id}, origin_content = \n{content}")
        # 补充审查内容信息，如 app_name, owner_id, owner_username, user_username 等
        self._enrich_content(content)
        # submit to moderation service
        content_json = content.model_dump_json(indent=4)
        logger.debug(f"Moderation content: message_id = {content.message_id}, enriched content = \n{content_json}")

        if not self.api_url:
            return SecurityModeration._default_pass_result("Moderation service is not enabled, pass immediately")

        headers = {
            'Content-Type': 'application/json'
        }
        if self.api_key:
            headers['Authorization'] = f"Bearer {self.api_key}"

        response = httpx_client.post(self.api_url, headers=headers, content=content_json, timeout=self.timeout)
        if response.status_code >= 300:
            raise httpx.RequestError(f"Moderation failed: {response.status_code} {response.text}")

        result = ModerationResult.model_validate_json(response.text)
        logger.debug(f"Moderation result: message_id = {content.message_id}, result = {result}")

        return result

    # 丰富审查内容信息
    def _enrich_content(self, content: ModerationContent) -> ModerationContent:
        """
        丰富审查内容信息，添加 app_name, owner_id, owner_username, user_username 等
        """

        with self.app.app_context():
            # 补充 app_name, owner_id, owner_username 信息
            if content.app_id and SecurityModeration._is_valid_uuid(content.app_id):
                app = db.session.query(App).filter(App.id == content.app_id).first()
                if app:
                    content.app_name = app.name
                    tenant_account_join = (db.session.query(TenantAccountJoin)
                                           .filter(TenantAccountJoin.tenant_id == app.tenant_id)
                                           .filter(TenantAccountJoin.role == "owner")
                                           .first())
                    owner = db.session.query(Account).filter(Account.id == tenant_account_join.account_id).first()
                    content.owner_id = owner.id
                    content.owner_username = owner.email[:owner.email.find("@")] if owner.email else ""

            # 补充 user_username 信息
            if content.user_id and SecurityModeration._is_valid_uuid(content.user_id):
                user_account = db.session.query(Account).filter(Account.id == content.user_id).first()
                if user_account:
                    content.user_username = user_account.email[:user_account.email.find("@")] if user_account.email else ""
                else:
                    # account 表没有查到，判定为 EndUser
                    end_user = db.session.query(EndUser).filter(EndUser.id == content.user_id).first()
                    if end_user and not end_user.is_anonymous:
                        # 通过 web sso 登录的用户，会把 email 作为 session_id
                        username = end_user.session_id
                        is_email = re.match(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$', username)
                        content.user_username = username[:username.find("@")] if is_email else username
                    else:
                        content.user_username = ""

        return content

    @staticmethod
    def _is_valid_uuid(value: str) -> bool:
        try:
            uuid.UUID(value)
            return True
        except ValueError:
            return False

    @staticmethod
    def _default_pass_result(message: str) -> ModerationResult:
        return ModerationResult(code=0, result="pass", message=message, risk_list=[])

security_moderation = SecurityModeration()

def init_app(app):
    security_moderation.init_app(app)