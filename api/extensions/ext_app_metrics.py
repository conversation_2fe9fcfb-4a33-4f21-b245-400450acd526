import json
import logging
import os
import threading

from flask import Response
from prometheus_client.utils import INF

from configs import dify_config
from dify_app import DifyApp
from .prometheus import LicloudMetrics, CustomCollector

logger = logging.getLogger(__name__)


def init_app(app: DifyApp):
    @app.after_request
    def after_request(response):
        """Add Version headers to the response."""
        response.headers.add("X-Version", dify_config.CURRENT_VERSION)
        response.headers.add("X-Env", dify_config.DEPLOY_ENV)
        return response

    @app.route("/health")
    def health():
        return Response(
            json.dumps({"pid": os.getpid(), "status": "ok", "version": dify_config.CURRENT_VERSION}),
            status=200,
            content_type="application/json",
        )

    @app.route("/threads")
    def threads():
        num_threads = threading.active_count()
        threads = threading.enumerate()

        thread_list = []
        for thread in threads:
            thread_name = thread.name
            thread_id = thread.ident
            is_alive = thread.is_alive()

            thread_list.append(
                {
                    "name": thread_name,
                    "id": thread_id,
                    "is_alive": is_alive,
                }
            )

        return {
            "pid": os.getpid(),
            "thread_num": num_threads,
            "threads": thread_list,
        }

    @app.route("/db-pool-stat")
    def pool_stat():
        from extensions.ext_database import db

        engine = db.engine
        # TODO: Fix the type error
        # FIXME maybe its sqlalchemy issue
        return {
            "pid": os.getpid(),
            "pool_size": engine.pool.size(),  # type: ignore
            "checked_in_connections": engine.pool.checkedin(),  # type: ignore
            "checked_out_connections": engine.pool.checkedout(),  # type: ignore
            "overflow_connections": engine.pool.overflow(),  # type: ignore
            "connection_timeout": engine.pool.timeout(),  # type: ignore
            "recycle_time": db.engine.pool._recycle,  # type: ignore
        }

    init_prometheus_metrics(app)

def init_prometheus_metrics(app: DifyApp):
    # init prometheus metrics
    metrics = LicloudMetrics(
        app=app,
        path="/dayu/prometheus",
        defaults_prefix="#no_prefix",
        buckets=(0.1, 0.2, 0.5, 1.0, 2.0, 5.0, 10.0, 15, 30, INF),
        group_by="url_rule",
        excluded_paths=["/health","/threads","/db-pool-stat","/dayu/metrics"],
    )
    component_name = os.environ.get("COMP_NAME", "licloud-dify-api")
    metrics.info(
        name='app_info',
        description='Application info',
        version=dify_config.CURRENT_VERSION,
    )

    # init custom metrics
    custom_collector = CustomCollector(registry=metrics.registry)
    custom_collector.register_thread_metrics()
    custom_collector.register_redis_metrics()
    custom_collector.register_sqlalchemy_metrics(app)
    custom_collector.start()
