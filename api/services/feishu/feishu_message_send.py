import json
import logging
import uuid
import re

from enum import Enum

import lark_oapi as lark
from lark_oapi.api.cardkit.v1 import *

from lark_oapi.api.im.v1 import \
    ReplyMessageRequest, ReplyMessageRequestBody, PatchMessageRequest, PatchMessageRequestBody, PatchMessageResponse, \
    ReplyMessageResponse, CreateMessageRequest, CreateMessageRequestBody, CreateMessageResponse

from libs.pooled_requests import requests


class SendMessageAction(Enum):
    """
    发送消息操作
    """
    SEND = "send"
    REPLY = "reply"
    PATCH = "patch"


class FeishuMessageSend:
    """Class for sending messages via Feishu."""

    @staticmethod
    def generate_streaming_card(element_id: str, closed: bool = False, im_message_id: str = "") -> str:
        """
        Generate the content for a streaming card.
        :param element_id: The unique identifier for the card element.
        :param closed: 是否关闭流式更新模式，默认为 False
        :param im_message_id:  message_id
        """

        feedback_element = FeishuMessageSend.generate_feedback_card(im_message_id=im_message_id, feedback_content="",
                                                                    rating="")

        elements = [
            {
                "tag": "markdown",
                "content": "思考中。。。",
                "element_id": element_id  ## 操作组件的唯一标识。用于后续增加、删除组件等操作。该属性需在卡片全局唯一。
            },
            feedback_element
        ]

        return json.dumps({
            "schema": "2.0",
            "config": {
                "streaming_mode": not closed,  ## 卡片是否处于流式更新模式，默认值为 false。
                "summary": {
                    "content": "生成中..."  ## 卡片在生成内容时展示的摘要。默认为 “[生成中...]”
                },
                "update_multi": True,
                "streaming_config": {

                }

            },
            "body": {
                "elements": elements
            }
        }, ensure_ascii=False)

    @staticmethod
    def has_create_card_permission(client: lark.Client) -> bool:
        """
        Check if the client has permission to create cards.
        :param client: The Lark client instance.
        :return: True if the client has permission, False otherwise.
        """
        try:
            request: CreateCardRequest = CreateCardRequest.builder() \
                .request_body(CreateCardRequestBody.builder()
                              .type("card_json")
                              .data(
                "{\"body\":{\"elements\":[{\"content\":\"卡片内容\",\"element_id\":\"markdown_1\",\"tag\":\"markdown\"}]},\"config\":{\"streaming_mode\":true,\"summary\":{\"content\":\"\"}},\"header\":{\"title\":{\"content\":\"卡片标题\",\"tag\":\"plain_text\"}},\"schema\":\"2.0\"}")
                              .build()) \
                .build()

            # 发起请求
            response: CreateCardResponse = client.cardkit.v1.card.create(request)
            return response.success()
        except Exception as e:
            logging.error(f"Failed to check create card permission: {e}")
            return False

    @staticmethod
    def create_and_send_streaming_card(client: lark.Client, message_id: str, event_id: str,
                                       element_id: str, im_message_id: str) -> str | None:
        """
        Create a streaming card in Feishu and return its card ID.
        If the card creation fails, it returns None.
        :param client: The Lark client instance.
        :param message_id: The ID of the message to which the card will be attached.
        :param event_id: The ID of the event for logging purposes.
        :param element_id: The unique identifier for the card element.
        :param im_message_id: The ID of the message to which the card will be attached.
        """
        # 构造请求对象
        request: CreateCardRequest = CreateCardRequest.builder() \
            .request_body(CreateCardRequestBody.builder()
                          .type("card_json")
                          .data(
            FeishuMessageSend.generate_streaming_card(element_id, closed=False, im_message_id=im_message_id)).build()) \
            .build()

        # 发起请求
        response: CreateCardResponse = client.cardkit.v1.card.create(request)

        # 处理失败返回
        if not response.success():
            logging.error(
                f"client.cardkit.v1.card.create failed, event_id: {event_id}, code: {response.code}, msg: {response.msg}, log_id: {response.get_log_id()}, resp: \n{json.dumps(json.loads(response.raw.content), indent=4, ensure_ascii=False)}")
            return None
        card_id = response.data.card_id
        message_content = json.dumps(
            {"type": "card", "data": {"card_id": card_id}}, ensure_ascii=False
        )  # send the card to the message
        message_resp: CreateMessageResponse = FeishuMessageSend.send_feishu_message(
            client=client,
            message_id=message_id,
            message_content=message_content,
            send_action=SendMessageAction.REPLY,
            event_id=event_id
        )
        if not message_resp.success():
            logging.error(
                f"client.im.v1.message.create failed, event_id: {event_id}, code: {message_resp.code}, msg: {message_resp.msg}, log_id: {message_resp.get_log_id()}, resp: \n{json.dumps(json.loads(message_resp.raw.content), indent=4, ensure_ascii=False)}")
            return None
        return card_id

    @staticmethod
    def send_streaming_card_update(client: lark.Client, event_id: str,
                                   element_id: str, card_id: str, content: str, sequence: int) -> Optional[
        ContentCardElementResponse]:

        if card_id is None or card_id.strip() == "":
            logging.warning(f"event_id:{event_id} Card ID is empty, cannot send streaming card update.")
            return None

        if content is None or content.strip() == "":
            logging.warning(f"event_id:{event_id} Content is empty, cannot send streaming card update.")
            return None

        logging.debug(
            f"event_id:{event_id} Content :[{content} ] , element_id: {element_id}, card_id: {card_id}, sequence: {sequence}")
        uuid_str: str = uuid.uuid4().hex

        # 构造请求对象
        request: ContentCardElementRequest = ContentCardElementRequest.builder() \
            .card_id(card_id=card_id) \
            .element_id(element_id=element_id) \
            .request_body(ContentCardElementRequestBody.builder()
                          .uuid(uuid_str)
                          .content(content) \
                          .sequence(sequence)
                          .build()) \
            .build()

        # 发起请求
        response: ContentCardElementResponse = client.cardkit.v1.card_element.content(request=request, option=None)

        # 处理失败返回
        if not response.success():
            logging.error(
                f"client.cardkit.v1.card_element.content failed, event_id:{event_id}, code: {response.code}, msg: {response.msg}, log_id: {response.get_log_id()}, resp: \n{json.dumps(json.loads(response.raw.content), indent=4, ensure_ascii=False)}")
        return response

    @staticmethod
    def send_feishu_message(client: lark.Client, message_id: str, message_content: str,
                            send_action: SendMessageAction, event_id: str) -> \
            Optional[
                Union[ReplyMessageResponse, PatchMessageResponse, CreateMessageResponse]]:

        """Send a message via Feishu."""

        logging.info(
            f"event_id:{event_id} Sending feishu message, message_id: {message_id} , action: {send_action}")
        try:
            if send_action == SendMessageAction.REPLY:
                request = ReplyMessageRequest.builder() \
                    .message_id(message_id) \
                    .request_body(
                    ReplyMessageRequestBody.builder().msg_type("interactive").content(message_content).build()) \
                    .build()
                return client.im.v1.message.reply(request)
            if send_action == SendMessageAction.SEND:
                request = CreateMessageRequest.builder() \
                    .receive_id_type("chat_id") \
                    .request_body(
                    CreateMessageRequestBody.builder().msg_type("interactive").receive_id(message_id).content(
                        message_content).build()) \
                    .build()
                return client.im.v1.message.create(request)

            elif send_action == SendMessageAction.PATCH:
                request = PatchMessageRequest.builder() \
                    .message_id(message_id) \
                    .request_body(PatchMessageRequestBody.builder().content(message_content).build()) \
                    .build()
                return client.im.v1.message.patch(request)
            else:
                logging.error(f"event_id:{event_id} Unsupported action: {send_action}")
                return None
        except Exception as e:
            logging.error(f"event_id:{event_id} Failed to send feishu message, error: {e}")
        return None

    @staticmethod
    def process_markdown_images(client: lark.Client, content: str, event_id: str = "") -> str:
        """
        处理markdown中的图片URL，上传到飞书并替换为image_key
        """
        # 正则表达式匹配markdown图片格式: ![alt](url)
        image_pattern = r'!\[([^\]]*)\]\(([^\)]+)\)'

        def replace_image(match):
            alt_text = match.group(1)
            image_url = match.group(2)

            try:
                # 上传图片获取image_key
                image_key = FeishuMessageSend.upload_image_from_url(client, image_url, event_id)
                if image_key:
                    # 替换为飞书图片格式
                    return f'![{alt_text}]({image_key})'
                else:
                    logging.warning(f"event_id:{event_id} Failed to upload image: {image_url}")
                    # 上传失败时，将图片转换为普通链接格式
                    return f'[{alt_text if alt_text else "图片链接"}]({image_url})'
            except Exception as e:
                logging.error(f"event_id:{event_id} Error processing image {image_url}: {e}")
                # 异常时也将图片转换为普通链接格式
                return f'[{alt_text if alt_text else "图片链接"}]({image_url})'

        # 替换所有图片URL
        processed_content = re.sub(image_pattern, replace_image, content)
        return processed_content

    @staticmethod
    def upload_image_from_url(client: lark.Client, image_url: str, event_id: str = "") -> Optional[str]:
        """
        从URL下载图片并上传到飞书，返回image_key
        """
        try:
            # 下载图片
            response = requests.get(image_url, timeout=30)
            response.raise_for_status()

            # 获取图片内容
            image_content = response.content

            # 检查文件大小（不超过10MB）
            if len(image_content) > 10 * 1024 * 1024:
                logging.warning(f"event_id:{event_id} Image too large: {len(image_content)} bytes")
                return None

            # 检查文件大小不为0
            if len(image_content) == 0:
                logging.warning(f"event_id:{event_id} Image size is 0")
                return None

            # 使用lark_oapi SDK上传图片
            from lark_oapi.api.im.v1 import CreateImageRequest, CreateImageRequestBody
            import io

            # 创建文件对象
            image_file = io.BytesIO(image_content)

            # 构造请求对象
            request = CreateImageRequest.builder() \
                .request_body(CreateImageRequestBody.builder()
                              .image_type("message")
                              .image(image_file)
                              .build()) \
                .build()

            # 发起请求
            upload_response = client.im.v1.image.create(request)

            # 处理失败返回
            if not upload_response.success():
                logging.error(
                    f"event_id:{event_id} 飞书图片上传失败, code: {upload_response.code}, msg: {upload_response.msg}")
                return None

            # 返回image_key
            image_key = upload_response.data.image_key
            logging.info(f"event_id:{event_id} Successfully uploaded image, image_key: {image_key}")
            return image_key

        except requests.RequestException as e:
            logging.error(f"event_id:{event_id} Network error downloading/uploading image {image_url}: {e}")
            return None
        except Exception as e:
            logging.error(f"event_id:{event_id} Error uploading image {image_url}: {e}")
            return None

    @staticmethod
    def generate_element_id() -> str:
        """
        生成符合要求的唯一 element_id，必须以字母开头，只能包含字母、数字和下划线，且不超过20个字符。
        """
        # 以字母开头，后面拼接15位 uuid，确保总长不超过20
        return f"e_{uuid.uuid4().hex[:15]}"

    @staticmethod
    def generate_feedback_card(im_message_id: str, feedback_content: str, rating: str):

        rating = "" if rating is None else rating

        elements = {
            "tag": "form",
            "elements": [
                {
                    "tag": "input",
                    "placeholder": {
                        "tag": "plain_text",
                        "content": "请输入您的反馈建议"
                    },
                    "default_value": feedback_content,
                    "max_length": 256,
                    "width": "fill",
                    "name": "feedback_content",
                    "margin": "0px 0px 0px 0px"
                },
                {
                    "tag": "column_set",
                    "columns": [
                        {
                            "tag": "column",
                            "width": "auto",
                            "elements": [
                                {
                                    "tag": "button",
                                    "text": {
                                        "tag": "plain_text",
                                        "content": "👍点赞"
                                    },
                                    "type": "primary" if "like" == rating else "default",
                                    "width": "default",
                                    "behaviors": [
                                        {
                                            "type": "callback",
                                            "value": {
                                                "feedback": "like",
                                                "im_message_id": f"{im_message_id}"
                                            }
                                        }
                                    ],
                                    "form_action_type": "submit",
                                    "name": "Button_like"
                                }
                            ],
                            "vertical_align": "top"
                        },
                        {
                            "tag": "column",
                            "width": "auto",
                            "elements": [
                                {
                                    "tag": "button",
                                    "text": {
                                        "tag": "plain_text",
                                        "content": "👎点踩"
                                    },
                                    "type": "primary" if "dislike" == rating else "default",
                                    "width": "default",
                                    "behaviors": [
                                        {
                                            "type": "callback",
                                            "value": {
                                                "feedback": "dislike",
                                                "im_message_id": f"{im_message_id}"
                                            }
                                        }
                                    ],
                                    "form_action_type": "submit",
                                    "name": "Button_dislike"
                                }
                            ],
                            "vertical_align": "top"
                        }
                    ]
                }
            ],
            "padding": "4px 0px 4px 0px",
            "margin": "0px 0px 0px 0px",
            "name": "Form_mbuklbki"
        }
        return elements
