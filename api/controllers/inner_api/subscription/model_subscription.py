from copy import deepcopy
from typing import Dict, Any
import logging
import os
from flask_restful import Resource
from cloudevents.http import from_http
from flask import request

from controllers.inner_api import api
from controllers.inner_api.subscription.utils import ToolsClient
from controllers.inner_api.wraps import inner_api_only
from controllers.console.wraps import setup_required
from services.model_provider_service import ModelProviderService
from licloud.apihub import DeployEnv

from .shared.models import SubscriptionEvent, ProjectType
from .shared.processor import SubscriptionEventProcessor

logger = logging.getLogger(__name__)

# 全局依赖
li_env = os.environ.get('LI_ENV', DeployEnv.PROD)
tools = ToolsClient()
model_provider_service = ModelProviderService()

# 工具类型模型映射
TOOL_DIFY_MAPPING = {
    'azure-gpt-image-1': 'azure_gpt_image_1',
    'ali-paraformer': 'ali_paraformer',
    'bing-natives': 'bing',
    'metaso': 'metaso',
    'wanx-v1': 'tongyiwanx',
    'jina': 'jinasearch'
}


def handle_model_subscription_approved(event: SubscriptionEvent) -> Dict[str, Any]:
    """处理模型订阅审核通过"""
    event_id = event.id
    data = event.data
    logger.info(f"[{event_id}] 🎉 Model subscription approved for {data.username}@{data.tenant_id}")

    try:
        # 获取租户ID
        tenant_id = tools.query_db_tenant_id(app_name=data.app)
        if not tenant_id:
            logger.error(f"[{event_id}] Failed to get tenant_id for app: {data.app}")
            raise ValueError(f"No tenant found for app: {data.app}")

        # 获取API凭证
        credentials = get_api_credentials(data.tokens, li_env)
        if not credentials:
            logger.error(f"[{event_id}] Failed to get API credentials")
            raise ValueError("No valid API credentials found")

        # 处理每个模型
        processed_models = []
        failed_models = []

        for project in data.projects:
            router_key = project.code
            logger.info(f"[{event_id}] Processing model: {project.name} ({router_key})")

            try:
                process_model_router_key(
                    event_id=event_id,
                    username=data.username,
                    tenant_id=tenant_id,
                    credentials=deepcopy(credentials),
                    router_key=router_key
                )
                processed_models.append(router_key)
                logger.info(f"[{event_id}] ✅ Successfully processed model: {router_key}")

            except Exception as e:
                logger.error(f"[{event_id}] ❌ Failed to process model {router_key}: {e}")
                failed_models.append({"router_key": router_key, "error": str(e)})

        logger.info(f"[{event_id}] ✅ Model subscription processing completed. "
                    f"Success: {len(processed_models)}, Failed: {len(failed_models)}")

        return {
            "status": "approved",
            "service_type": "model",
            "event_id": event_id,
            "audit_id": data.audit_id,
            "process_id": data.process_id,
            "username": data.username,
            "tenant_id": tenant_id,
            "app": data.app,
            "processed_models": processed_models,
            "failed_models": failed_models,
            "message": f"Model subscription processed. Success: {len(processed_models)}, Failed: {len(failed_models)}"
        }

    except Exception as e:
        logger.error(f"[{event_id}] 💥 Error in model subscription approval handler: {e}")
        raise


def get_api_credentials(tokens, target_env) -> Dict[str, str]:
    """获取API凭证"""
    env_key = target_env if target_env == DeployEnv.PROD else DeployEnv.ONTEST
    token = tokens.get_token_for_env(env_key)

    if token:
        return {"api_key": token}
    return {}


def process_model_router_key(event_id: str, username: str, tenant_id: str, credentials: Dict, router_key: str):
    """处理模型路由键"""
    if not router_key:
        logger.warning(f"[{event_id}] Empty router key, skip")
        return

    # 检查是否为工具类型模型
    if router_key in TOOL_DIFY_MAPPING:
        logger.info(f"[{event_id}] Processing as tool model: {router_key}")
        tool_credentials = get_tool_credentials(router_key, credentials)
        tools.add_tool_dify(
            username=username,
            tenant_id=tenant_id,
            provider=TOOL_DIFY_MAPPING[router_key],
            credentials=tool_credentials
        )
        return

    # 处理普通模型
    logger.info(f"[{event_id}] Processing as regular model: {router_key}")
    model_provider, model_type = tools.get_llmgateway_info(routerkey=router_key)

    if not all((model_provider, model_type)):
        logger.error(f"[{event_id}] No llmgateway info found for router_key: {router_key}")
        raise ValueError(f"No llmgateway info found for router_key: {router_key}")

    model_provider_service.save_model_credentials(
        tenant_id=tenant_id,
        provider=model_provider,
        model=router_key,
        model_type=model_type,
        credentials=credentials
    )


def get_tool_credentials(router_key: str, credentials: Dict) -> Dict:
    """获取工具类型模型的凭证配置"""
    api_key = credentials.get("api_key")

    if router_key == 'wanx-v1':
        return {
            "dashscope_api_key": api_key,
            "base_address": "http://api-hub.inner.chj.cloud/bcs-apihub-ai-proxy-service/apihub/bailian/v1.0/wanx-async-v1"
        }
    elif router_key == 'bing-natives':
        return {
            "subscription_key": api_key,
            "server_url": "https://apihub-dify-adaptor.fc.chj.cloud/providers/bing/paths",
            "allow_related_searches": False,
            "allow_web_pages": True,
            "allow_news": False,
            "allow_computation": False
        }
    else:
        return {"subscription_key": api_key}


class ModelSubscription(Resource):
    """模型订阅API - routerkey:provider"""

    def __init__(self):
        # 只处理 LiCloudAI 类型的应用
        self.processor = SubscriptionEventProcessor(
            approved_handler=handle_model_subscription_approved,
            app_type_filter="LiCloudAI",
            project_type_filter=[ProjectType.AI_NATIVE, ProjectType.AI_TOOLS]
        )

    @setup_required
    @inner_api_only
    def post(self):
        """处理模型订阅事件"""
        event_id = None
        try:
            # 解析原始事件
            raw_event = from_http(request.headers, request.get_data())
            event_id = raw_event.get('id', 'unknown')

            logger.info(f"[{event_id}] 📨 Received model subscription event, "
                        f"source={raw_event.get('source')}, type={raw_event.get('type')}")

            # 解析为结构化事件
            event = self.processor.parse_event(raw_event)
            if event is None:
                logger.debug(f"[{event_id}] Event filtered out, skipping processing")
                return "", 200

            # 处理模型订阅事件
            result = self.processor.process(event)
            logger.info(f"[{event_id}] ✅ Model subscription event processing completed")

            return {"success": True, "data": result}, 200

        except Exception as e:
            logger.error(f"[{event_id or 'unknown'}] 💥 Unexpected error processing model subscription event: {e}")
            return {"error": "Internal server error"}, 500


# 注册API路由
api.add_resource(ModelSubscription, '/enterprise/model/subscriptions')