import json
from typing import Any

from core.mcp.types import Tool as RemoteMCPTool
from core.tools.entities.common_entities import I18nObject
from core.tools.entities.tool_entities import (
    ToolDescription,
    ToolIdentity,
    ToolProviderIdentity,
    ToolProviderType, ToolProviderCredentials,
)
from core.tools.mcp_tool.tool import MCPTool
from core.tools.provider.tool_provider import ToolProviderController
from core.tools.tool.tool import Tool
from models.tools import MCPToolProvider
from services.tools.tools_transform_service import ToolTransformService


class MCPToolProviderController(ToolProviderController):
    provider_id: str
    tenant_id: str
    user_id: str
    server_url: str

    @property
    def provider_type(self) -> ToolProviderType:
        """
        returns the type of the provider

        :return: type of the provider
        """
        return ToolProviderType.MCP

    @classmethod
    def from_db(cls, db_provider: MCPToolProvider) -> "MCPToolProviderController":
        """
        from db provider
        """
        tools = []
        tools_data = json.loads(db_provider.tools)
        remote_mcp_tools = [RemoteMCPTool(**tool) for tool in tools_data]
        user = db_provider.load_user()
        tools = [
            Tool(
                identity=ToolIdentity(
                    author=db_provider.author,
                    name=remote_mcp_tool.name,
                    label=I18nObject(en_US=remote_mcp_tool.name, zh_Hans=remote_mcp_tool.name),
                    provider=db_provider.name,
                    icon=db_provider.icon,
                ),
                parameters=ToolTransformService.convert_mcp_schema_to_parameter(remote_mcp_tool.inputSchema),
                description=ToolDescription(
                    human=I18nObject(
                        en_US=remote_mcp_tool.description or "", zh_Hans=remote_mcp_tool.description or ""
                    ),
                    llm=remote_mcp_tool.description or "",
                ),
                output_schema=None,
                has_runtime_parameters=len(remote_mcp_tool.inputSchema) > 0,
            )
            for remote_mcp_tool in remote_mcp_tools
        ]

        return cls(
            provider_id=db_provider.id or "",
            tenant_id=db_provider.tenant_id,
            user_id=db_provider.user_id or "",
            server_url=db_provider.server_url,
            identity=ToolProviderIdentity(
                author=user.name if user else "Anonymous",
                name=db_provider.name,
                label=I18nObject(en_US=db_provider.name, zh_Hans=db_provider.name),
                description=I18nObject(en_US="", zh_Hans=""),
                icon=db_provider.icon,
            ),
            tools=tools,
            credentials_schema={
                "server_url": ToolProviderCredentials(
                    name="server_url",
                    required=True,
                    type=ToolProviderCredentials.CredentialsType.SECRET_INPUT,
                    help=I18nObject(en_US="The address of mcp server", zh_Hans="MCP Server 地址"),
                ),
                "subscription_key": ToolProviderCredentials(
                    name="subscription_key",
                    required=True,
                    type=ToolProviderCredentials.CredentialsType.SECRET_INPUT,
                    help=I18nObject(en_US="The subscription key for mcp server", zh_Hans="MCP Server 订阅密钥"),
                ),
            },
        )

    def _validate_credentials(self, user_id: str, credentials: dict[str, Any]) -> None:
        """
        validate the credentials of the provider
        """
        pass

    def get_tool(self, tool_name: str) -> MCPTool:  # type: ignore
        """
        return tool with given name
        """
        tool = next(
            (tool for tool in self.tools if tool.identity.name == tool_name), None
        )

        if not tool:
            raise ValueError(f"Tool with name {tool_name} not found")

        return MCPTool(
            provider_id=self.provider_id,
            tenant_id=self.tenant_id,
            icon=self.identity.icon,
            user_id=self.user_id,
            server_url=self.server_url,
            identity=tool.identity,
            parameters=tool.parameters,
            description=tool.description,
        )

    def get_tools(self, user_id: str = "", tenant_id: str = "") -> list[Tool]:  # type: ignore
        """
        get all tools
        """
        return [
            MCPTool(
                provider_id=self.provider_id,
                tenant_id=self.tenant_id,
                icon=self.identity.icon,
                user_id=self.user_id,
                server_url=self.server_url,
                identity=tool.identity,
                parameters=tool.parameters,
                description=tool.description,
            )
            for tool in self.tools
        ]
