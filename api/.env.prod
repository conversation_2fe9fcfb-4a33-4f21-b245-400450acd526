# Your App secret key will be used for securely signing the session cookie
# Make sure you are changing this key for your deployment with a strong key.
# You can generate a strong key using `openssl rand -base64 42`.
# Alternatively you can set it with `SECRET_KEY` environment variable.
SECRET_KEY=uttlBXq7TbXu5B1z4kMUXupykN5BUrp7xR23Z9zuJC2uge9eI4TQK4pY

# Console API base URL
CONSOLE_API_URL=https://liai-builder.chj.cloud
CONSOLE_WEB_URL=https://liai-builder.chj.cloud

# Service API base URL
SERVICE_API_URL=https://liai-app.chj.cloud

# Web APP base URL
APP_WEB_URL=https://liai-app.chj.cloud
APP_API_URL=https://liai-app.chj.cloud

# celery configuration
CELERY_BROKER_URL="redis://:hV29cvyScm^<EMAIL>:16379/1"

# redis configuration
REDIS_HOST=licloud-dify-prod-default-prod.chj.cloud
REDIS_PORT=16379
REDIS_USERNAME=
REDIS_PASSWORD=hV29cvyScm^N
REDIS_DB=0

# PostgreSQL database configuration
DB_USERNAME=licloud_dify_rw
DB_PASSWORD='ADFAladf@ad!@EeA23'
DB_HOST=licloud-dify-prod.rdsg9lcw2btw6ao.rds.bj.baidubce.com
DB_PORT=3306
DB_DATABASE=licloud_dify
SQLALCHEMY_POOL_SIZE=10
SQLALCHEMY_MAX_OVERFLOW=50
SQLALCHEMY_POOL_PRE_PING=true


# Storage configuration
# use for store upload files, private keys...
# storage type: local, s3, azure-blob
STORAGE_TYPE=s3


# S3 Storage configuration
S3_ENDPOINT=https://s3.bj.bcebos.com
S3_BUCKET_NAME=prod-cnhb01-liai-dify
S3_REGION=bj
#S3_ACCESS_KEY_CONJUR_PATH=Prd_Vault/authn/App_licloud-dify_All/App_licloud-dify_BaiduCloud_AKSK/username
#S3_SECRET_KEY_CONJUR_PATH=Prd_Vault/authn/App_licloud-dify_All/App_licloud-dify_BaiduCloud_AKSK/password
S3_ACCESS_KEY=ALTAKgFpfT9PLMkjKc993xdIuR
S3_SECRET_KEY=30fa91e0c2944883bfec1c4c1bd1ffc0


# CORS configuration
WEB_API_CORS_ALLOW_ORIGINS=*
CONSOLE_CORS_ALLOW_ORIGINS=*

# Vector database configuration, support: weaviate, qdrant, milvus, relyt, pgvecto_rs, pgvector
VECTOR_STORE=adbpg

# PGVector configuration
PGVECTOR_HOST=gp-2ze82shp5fdimlea7-master.gpdb.rds.aliyuncs.com
PGVECTOR_PORT=5432
PGVECTOR_USER=cloud_vector_prod_rw
PGVECTOR_PASSWORD=44e22ieS45DXoot#kg466
PGVECTOR_DATABASE=cloud_vector_prod
PGVECTOR_MIN_CACHED=1
PGVECTOR_MAX_CACHED=5
PGVECTOR_MAX_CONNECTIONS=10

# ADBPG 7.0 configuration
ADBPG_HOST=gp-2ze9cg45f792gr8ci-master.gpdb.rds.aliyuncs.com
ADBPG_PORT=5432
ADBPG_USER=licloud_dify_vector_rw
ADBPG_PASSWORD=b4b6e20efe#3a
ADBPG_DATABASE=licloud_dify_vector
ADBPG_MIN_CACHED=1
ADBPG_MAX_CACHED=5
ADBPG_MAX_CONNECTIONS=10

# Upload configuration
UPLOAD_FILE_SIZE_LIMIT=100
UPLOAD_FILE_BATCH_LIMIT=5
UPLOAD_IMAGE_FILE_SIZE_LIMIT=10

# File configuration
FILES_ACCESS_TIMEOUT=86400

# Model Configuration
MULTIMODAL_SEND_IMAGE_FORMAT=base64

# Sentry configuration
SENTRY_DSN=

# DEBUG
DEBUG=false
SQLALCHEMY_ECHO=false

ETL_TYPE=dify
UNSTRUCTURED_API_URL=

SSRF_PROXY_HTTP_URL=http://licloud-dify-ssrf-proxy:3128
SSRF_PROXY_HTTPS_URL=http://licloud-dify-ssrf-proxy:3128

BATCH_UPLOAD_LIMIT=10
KEYWORD_DATA_SOURCE_TYPE=database

# CODE EXECUTION CONFIGURATION
CODE_EXECUTION_ENDPOINT=http://licloud-dify-sandbox.cn01-prod-devops.svc.cluster.local:8194
CODE_EXECUTION_API_KEY=7KiAoeCsjwgNZrSBx806WOG5Uy8bfnSP8frA4Xyhdk+1tO9RIkntqDt4
CODE_MAX_NUMBER=9223372036854775807
CODE_MIN_NUMBER=-9223372036854775808
CODE_MAX_STRING_LENGTH=80000
TEMPLATE_TRANSFORM_MAX_LENGTH=80000
CODE_MAX_STRING_ARRAY_LENGTH=80000
CODE_MAX_OBJECT_ARRAY_LENGTH=80000
CODE_MAX_NUMBER_ARRAY_LENGTH=1000
CODE_EXECUTION_CONNECT_TIMEOUT=10
CODE_EXECUTION_READ_TIMEOUT=600
CODE_EXECUTION_WRITE_TIMEOUT=600

# API Tool configuration
API_TOOL_DEFAULT_CONNECT_TIMEOUT=10
API_TOOL_DEFAULT_READ_TIMEOUT=600

# HTTP Node configuration
HTTP_REQUEST_MAX_CONNECT_TIMEOUT=300
HTTP_REQUEST_MAX_READ_TIMEOUT=600
HTTP_REQUEST_MAX_WRITE_TIMEOUT=600
HTTP_REQUEST_NODE_MAX_BINARY_SIZE=10485760 # 10MB
HTTP_REQUEST_NODE_MAX_TEXT_SIZE=1048576 # 1MB

# Workflow runtime configuration
WORKFLOW_MAX_EXECUTION_STEPS=500
WORKFLOW_MAX_EXECUTION_TIME=1200
WORKFLOW_CALL_MAX_DEPTH=10

# Log file path
LOG_LEVEL=DEBUG
# LOG_FILE=/chj/data/log/licloud-dify-app-api/licloud-dify-app-api.log
LOG_FORMAT='[v1] [%(asctime)s.%(msecs)03d] [%(levelname)s] [%(threadName)s] %(filename)s:%(lineno)d [TID: N/A] %(message)s'
LOG_DATEFORMAT='%Y-%m-%d %H:%M:%S'
LOG_TZ=Asia/Shanghai


# IDAAS configuration
IDAAS_CLIENT_ID=5G5WX45otLiQSUECWT6S62
IDAAS_CLIENT_SECRET=eyJrdHkiOiJvY3QiLCJraWQiOiJ0RHpjYnh0X0FRIiwiYWxnIjoiSFMyNTYiLCJrIjoidXZZVkVuMFpGN1RCOHJHWUU3a0N2RFVNRHlWMkU3TkRQM2JkVnA5b3FlWSJ9
IDAAS_AUTH_URL=https://id.lixiang.com/api/auth
IDAAS_TOKEN_URL=https://id.lixiang.com/api/token
IDAAS_USER_INFO_URL=https://li.chj.cloud/licloud-iam-service/v1/users/profile
IDAAS_SERVICE_ID=1TmoBZo2H9TXGvdOkHDckf
IAM_QUERY_USER_URL=https://li.chj.cloud/licloud-iam-service/v1/users/detail
IAM_USER_INFO_URL=https://li.chj.cloud/licloud-iam-service/v1/users/profile


#INNER API
INNER_API=true
INNER_API_KEY=TT7yx9BIM9PdsBolzTp05EyPFgcgPgQeRogiAZWRv9Fb

#MODE
HOSTED_FETCH_APP_TEMPLATES_MODE=builtin

# Enterprise Enable
ENTERPRISE_ENABLED="true"
ENTERPRISE_API_URL="https://dify-enterprise-cn01.inner.chj.cloud/inner/api"
ENTERPRISE_API_SECRET_KEY="vO9+IXg0ER6tqz4kdhERidWh5ErlKzOEtmP5qfYDYbU="

# ZHIXIAO Base URL
ZHIXIAO_BASE_URL=https://cfe-feishu-server.chehejia.com
ZHIXIAO_SIGN_SECRET_KEY=ohr4p9qg92x6eth8
ZHIXIAO_SIGN_CLIENT_ID=cli_a2efbd090abad013

# Security moderation configuration
SECURITY_MODERATION_API_URL=https://cs-api.inner.chj.cloud/api/v1/api_hub/sync_audit
SECURITY_MODERATION_TIMEOUT=5
SECURITY_MODERATION_MAX_WORKERS=10

# IDaaS M2M Auth
V4_CLIENT_ID=gc9qs11DfcEmdpNaxjKrP
V4_CLIENT_SECRET_KEY=eyJrdHkiOiJvY3QiLCJraWQiOiJTMTlOZGJ2TUl3IiwiYWxnIjoiSFMyNTYiLCJrIjoibjJtbFdzZ0o3NFpRbU44VWp2TnktMGFNRGw0cGpBZ1RUYjRKQVJOWEVTTSJ9
V4_SERVICE_ID=1tmGCFoJSQiQmR46HK97rX
V4_SERVICE_SECRET_KEY=eyJrdHkiOiJvY3QiLCJraWQiOiJ6ZXd3WGtybWxnIiwiYWxnIjoiSFMyNTYiLCJrIjoiaEE3VjJlVEdLQ0RnV1FFUnZoLVlxYkp4elhBcXZpY2dUQjhqa3B1UnRDcyJ9
IDAAS_MGMT_API_SERVICE_ID=XdEjcBLH3AShKophv6p7h
LIID_ENDPOINT=https://liid.lixiang.com
IDAAS_ENDPOINT=https://id.lixiang.com/api
LIID_DOMAIN_ID=1ePmlAVpLI6JX37PjEqH6B
M2M_AUTH_ENABLED=false

IT_COA_URL=https://coa-api.it.lixiangoa.com
IT_COA_CLIENT_ID=278
IT_COA_CLIENT_SECRET=eXSWimhCvT31jm4yKBHE679vhREM3N7WyhxpH4Iz
FEISHU_WEBHOOK_URL=https://openapi.chehejia.com/licloud-dify-app-api