from sqlalchemy.testing.plugin.plugin_base import logging

from libs.httpx import httpx_client
from extensions.ext_redis import redis_client
from services.enterprise.base import EnterpriseRequest

default_setting = {
    "enable_sync_moderation": False,
    "can_enable_web_app": True,
    "can_enable_service_api": True,
}

class EnterpriseService:
    @classmethod
    def get_info(cls):
        return EnterpriseRequest.send_request("GET", "/info")

    @classmethod
    def get_app_web_sso_enabled(cls, app_code):
        return EnterpriseRequest.send_request("GET", f"/app-sso-setting?appCode={app_code}")

    @classmethod
    def get_app_security_setting(cls, app_id):
        headers = {"Content-Type": "application/json", "Enterprise-Api-Secret-Key": EnterpriseRequest.secret_key}

        url = f"{EnterpriseRequest.base_url}/app-security-setting?appID={app_id}"
        try:
            response = httpx_client.request("GET", url, headers=headers, timeout=2)
            if response.status_code == 200:
                return response.json()
            elif response.status_code == 404:
                return default_setting
            else:
                logging.error(f"Failed to get app security setting, status code: {response.status_code}, body: {response.text}")
                return default_setting
        except Exception as e:
            logging.error(f"Failed to get app security setting: {e}")
            return default_setting

    @classmethod
    def is_sync_moderation(cls, app_id) -> bool:
        key = f"enterprise:app:{app_id}:sync_moderation"
        cached_result = redis_client.get(key)
        if cached_result:
            return cached_result.decode("utf-8") == "true"

        setting = cls.get_app_security_setting(app_id)
        result = setting["enable_sync_moderation"]
        redis_client.setex(key, 600, "true" if result else "false")
        return result